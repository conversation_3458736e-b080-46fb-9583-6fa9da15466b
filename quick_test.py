#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速多轮对话测试脚本
用于快速验证 taxi_agent_system 的基本多轮对话功能
"""

import time
from taxi_agent_system import MainAgent


def test_basic_conversation():
    """测试基本对话功能"""
    print("🧪 基本对话功能测试")
    print("="*50)

    agent = MainAgent()
    session_id = agent.start_new_session("basic_test")
    
    # 基本测试用例
    test_cases = [
        "你好",
        "西湖在哪里？",
        "北京有哪些星巴克？",
        "我要打车",
        "从康德大厦到大悦城"
    ]
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n第{i}轮:")
        print(f"👤 用户: {user_input}")
        
        try:
            start_time = time.time()
            response = agent.process_user_input(user_input,session_id)
            response_time = time.time() - start_time
            
            print(f"🤖 助手: {response}")
            print(f"⏱️  响应时间: {response_time:.2f}秒")
            print("✅ 成功")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)


def test_context_memory():
    """测试上下文记忆"""
    print("\n🧠 上下文记忆测试")
    print("="*50)
    
    agent = MainAgent()
    session_id = agent.start_new_session("context_test")
    
    # 上下文相关的对话
    context_flow = [
        "我想去西湖",
        "那里怎么样？",  # 测试"那里"的指代
        "从那里到杭州东站怎么走？",  # 测试起点记忆
        "打车要多少钱？",  # 测试路线记忆
        "好的，帮我叫车"  # 测试完整信息记忆
    ]
    
    for i, user_input in enumerate(context_flow, 1):
        print(f"\n第{i}轮:")
        print(f"👤 用户: {user_input}")
        
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"🤖 助手: {response}")
            print("✅ 成功")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)


def test_parameter_confirmation():
    """测试参数确认流程"""
    print("\n✅ 参数确认流程测试")
    print("="*50)
    
    agent = MainAgent()
    session_id = agent.start_new_session("confirmation_test")
    
    # 需要确认的打车流程
    confirmation_flow = [
        "帮我叫车",
        "从北京大学",
        "到清华大学",
        "要豪华型车辆",
        "确认起点",
        "确认终点"
    ]
    
    for i, user_input in enumerate(confirmation_flow, 1):
        print(f"\n第{i}轮:")
        print(f"👤 用户: {user_input}")
        
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"🤖 助手: {response}")
            print("✅ 成功")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)


def test_error_handling():
    """测试错误处理"""
    print("\n🚨 错误处理测试")
    print("="*50)
    
    agent = MainAgent()
    session_id = agent.start_new_session("error_test")
    
    # 包含错误的输入
    error_cases = [
        "我要去火星",  # 无效地点
        "从不存在的地方",  # 无效起点
        "到另一个不存在的地方",  # 无效终点
        "我要去北京",  # 恢复正常
        "从天安门到鸟巢"  # 正常请求
    ]
    
    for i, user_input in enumerate(error_cases, 1):
        print(f"\n第{i}轮:")
        print(f"👤 用户: {user_input}")
        
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"🤖 助手: {response}")
            print("✅ 处理成功")
            
        except Exception as e:
            print(f"❌ 系统错误: {e}")
        
        print("-" * 30)


def test_multi_session():
    """测试多会话管理"""
    print("\n👥 多会话管理测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 模拟3个不同用户的会话
    sessions = [
        {"id": "user_1", "request": "我在北京，想去西湖"},
        {"id": "user_2", "request": "帮我找上海的星巴克"},
        {"id": "user_3", "request": "从广州塔到白云机场怎么走"},
        {"id": "user_1", "request": "那里有什么好玩的？"},  # 测试user_1的上下文
        {"id": "user_2", "request": "推荐最近的一家"},  # 测试user_2的上下文
    ]
    
    for i, session in enumerate(sessions, 1):
        session_id = session["id"]
        user_input = session["request"]
        
        print(f"\n第{i}轮 (会话: {session_id}):")
        print(f"👤 用户: {user_input}")
        
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"🤖 助手: {response}")
            print("✅ 成功")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)


def run_performance_test():
    """运行性能测试"""
    print("\n⚡ 性能测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 简单的性能测试
    test_requests = [
        "西湖在哪里？",
        "北京有哪些星巴克？",
        "从天安门到鸟巢怎么走？",
        "帮我叫车",
        "取消订单"
    ]
    
    total_time = 0
    success_count = 0
    response_times = []
    
    print(f"执行 {len(test_requests)} 个请求...")
    
    for i, request in enumerate(test_requests, 1):
        print(f"请求 {i}: {request}")
        
        start_time = time.time()
        try:
            response = agent.process_user_input(request, f"perf_test_{i}")
            response_time = time.time() - start_time
            response_times.append(response_time)
            success_count += 1

            print(f"  ✅ 成功 ({response_time:.2f}s)")
            
        except Exception as e:
            response_time = time.time() - start_time
            print(f"  ❌ 失败 ({response_time:.2f}s): {e}")
        
        total_time += response_time
    
    # 统计结果
    print(f"\n📊 性能统计:")
    print(f"总请求数: {len(test_requests)}")
    print(f"成功请求: {success_count}")
    print(f"成功率: {success_count/len(test_requests)*100:.1f}%")
    print(f"总耗时: {total_time:.2f}秒")
    
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"平均响应时间: {avg_time:.2f}秒")
        print(f"最快响应: {min_time:.2f}秒")
        print(f"最慢响应: {max_time:.2f}秒")


def main():
    """主测试函数"""
    print("🚀 快速多轮对话测试开始")
    print("="*60)
    
    start_time = time.time()
    
    try:
        # 运行各项测试
        test_basic_conversation()
        test_context_memory()
        test_parameter_confirmation()
        test_error_handling()
        test_multi_session()
        run_performance_test()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    
    total_time = time.time() - start_time
    
    print("\n" + "="*60)
    print("🏁 快速测试完成")
    print(f"总耗时: {total_time:.2f}秒")
    print("="*60)


if __name__ == "__main__":
    main()
