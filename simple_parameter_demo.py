#!/usr/bin/env python3
"""
简化的参数验证演示
直接展示4状态参数验证：未填写 -> 未校验 -> 已校验待确认 -> 已确认
"""

import os
import json
from datetime import datetime
from parameter_validation import FunctionParameterManager, ParameterState
from amap_mcp_tools import AmapMCPTools

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_parameter_states(manager, function_name):
    """打印参数状态"""
    try:
        status = manager.get_function_status(function_name)
        if not status.get("status"):
            print(f"❌ 获取状态失败: {status.get('error')}")
            return
        
        print(f"\n📊 {function_name} 参数状态:")
        print(f"   🔧 容错率: {status.get('fault_tolerance', '未知')}")
        
        parameters = status.get("parameters", {})
        for param_name, param_info in parameters.items():
            state = param_info.get("state", "未知")
            value = param_info.get("value", "无")
            confusion_score = param_info.get("confusion_score")
            
            # 状态图标
            state_icons = {
                "未填写": "⚪",
                "未校验": "🟡", 
                "已校验待确认": "🟠",
                "已确认": "🟢"
            }
            icon = state_icons.get(state, "❓")
            
            print(f"   {icon} {param_name}: {state}")
            print(f"      值: {value}")
            if confusion_score is not None:
                level = "低" if confusion_score <= 0.2 else "中" if confusion_score <= 0.5 else "高"
                print(f"      困惑度: {confusion_score:.2f} ({level})")
        
        execution_check = status.get("execution_check", {})
        can_execute = execution_check.get("can_execute", False)
        print(f"\n   🚀 执行状态: {'✅ 可执行' if can_execute else '❌ 不可执行'}")
        
        if not can_execute:
            missing = execution_check.get("missing_required", [])
            unvalidated = execution_check.get("unvalidated_params", [])
            unconfirmed = execution_check.get("unconfirmed_params", [])
            
            if missing:
                print(f"      ⚠️  缺少必填: {missing}")
            if unvalidated:
                print(f"      ⚠️  未校验: {unvalidated}")
            if unconfirmed:
                print(f"      ⚠️  未确认: {unconfirmed}")
                
    except Exception as e:
        print(f"❌ 打印状态出错: {str(e)}")

def demonstrate_parameter_flow():
    """演示完整的参数验证流程"""
    print_header("打车参数4状态验证流程演示")
    
    print("🎯 演示目标:")
    print("   1. 展示参数从'未填写'到'已确认'的完整流程")
    print("   2. 确保容错率低的函数参数都经过用户确认")
    print("   3. 演示不同困惑度对验证结果的影响")
    
    # 初始化参数管理器
    try:
        amap_tools = AmapMCPTools()
        manager = FunctionParameterManager(amap_tools)
        print("\n✅ 参数管理器初始化成功")
    except Exception as e:
        print(f"\n⚠️  参数管理器初始化失败: {e}")
        manager = FunctionParameterManager()
        print("✅ 使用简化版参数管理器")
    
    function_name = "call_taxi_service"
    
    # 步骤1: 初始状态 - 所有参数未填写
    print_header("步骤1: 初始状态 - 参数未填写")
    print("🔄 初始化函数参数（空参数）...")
    
    init_result = manager.initialize_function_parameters(function_name, {})
    print(f"✅ 初始化结果: {init_result.get('status', False)}")
    print(f"📝 必填参数: {init_result.get('required_params', [])}")
    print(f"📝 缺少参数: {init_result.get('missing_required', [])}")
    
    print_parameter_states(manager, function_name)
    input("\n按回车继续到步骤2...")
    
    # 步骤2: 提供参数 - 变为未校验状态
    print_header("步骤2: 提供参数 - 未校验状态")
    print("🔄 用户提供起点和终点参数...")
    
    params = {
        "start_place": "方正大厦",
        "end_place": "大兴机场"
    }
    
    init_result = manager.initialize_function_parameters(function_name, params)
    print(f"✅ 参数初始化: {init_result.get('status', False)}")
    print(f"📝 提供的参数: {list(params.keys())}")
    
    print_parameter_states(manager, function_name)
    input("\n按回车继续到步骤3...")
    
    # 步骤3: 验证参数 - 变为已校验待确认状态
    print_header("步骤3: 验证参数 - 已校验待确认状态")
    print("🔍 开始验证参数...")
    
    validation_result = manager.validate_all_parameters(function_name)
    print(f"✅ 验证完成: {validation_result.get('all_valid', False)}")
    
    validation_results = validation_result.get("validation_results", {})
    for param_name, result in validation_results.items():
        is_valid = result.get("is_valid", False)
        confusion_score = result.get("confusion_score", 0.0)
        print(f"   📊 {param_name}: {'✅ 有效' if is_valid else '❌ 无效'} (困惑度: {confusion_score:.2f})")
    
    print_parameter_states(manager, function_name)
    
    # 检查执行条件
    execution_check = manager.can_execute_function(function_name)
    print(f"\n🚀 执行检查: {'✅ 可执行' if execution_check.get('can_execute') else '❌ 需要确认'}")
    
    fault_tolerance = execution_check.get("fault_tolerance", "未知")
    print(f"🔧 函数容错率: {fault_tolerance}")
    
    if fault_tolerance == "低":
        print("⚠️  低容错率函数需要用户确认所有参数")
        unconfirmed = execution_check.get("unconfirmed_params", [])
        if unconfirmed:
            print(f"📋 需要确认的参数: {unconfirmed}")
    
    input("\n按回车继续到步骤4...")
    
    # 步骤4: 确认参数 - 变为已确认状态
    print_header("步骤4: 确认参数 - 已确认状态")
    print("✅ 用户确认参数...")
    
    # 确认所有参数
    for param_name in ["start_place", "end_place"]:
        confirm_result = manager.confirm_parameter(function_name, param_name)
        status = confirm_result.get("status", False)
        print(f"   ✅ 确认 {param_name}: {'成功' if status else '失败'}")
        if not status:
            print(f"      ❌ 错误: {confirm_result.get('error', '未知错误')}")
    
    print_parameter_states(manager, function_name)
    
    # 最终执行检查
    final_check = manager.can_execute_function(function_name)
    can_execute = final_check.get("can_execute", False)
    print(f"\n🎯 最终结果: {'✅ 可以执行打车服务' if can_execute else '❌ 仍不能执行'}")
    
    if can_execute:
        print("🚀 所有参数已确认，可以安全执行低容错率的打车服务！")
    else:
        print(f"❌ 执行失败原因: {final_check}")

def demonstrate_different_scenarios():
    """演示不同场景下的参数验证"""
    print_header("不同场景的参数验证演示")
    
    try:
        amap_tools = AmapMCPTools()
        manager = FunctionParameterManager(amap_tools)
    except:
        manager = FunctionParameterManager()
    
    scenarios = [
        {
            "name": "场景1: 高容错率函数",
            "function": "mcp_search_poi",
            "params": {"keyword": "星巴克"},
            "description": "搜索POI，容错率高，不需要用户确认"
        },
        {
            "name": "场景2: 低容错率函数",
            "function": "call_taxi_service", 
            "params": {"start_place": "方正大厦", "end_place": "大兴机场"},
            "description": "打车服务，容错率低，需要用户确认"
        },
        {
            "name": "场景3: 价格估算函数",
            "function": "mcp_estimate_taxi_price",
            "params": {"origin_poi": "方正大厦", "dest_poi": "大兴机场"},
            "description": "价格估算，容错率低，需要用户确认"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🧪 {scenario['name']}")
        print(f"   📝 {scenario['description']}")
        print(f"   🔧 函数: {scenario['function']}")
        print(f"   📊 参数: {scenario['params']}")
        
        # 初始化和验证
        init_result = manager.initialize_function_parameters(
            scenario['function'], 
            scenario['params']
        )
        
        if init_result.get("status"):
            validation_result = manager.validate_all_parameters(scenario['function'])
            execution_check = manager.can_execute_function(scenario['function'])
            
            fault_tolerance = execution_check.get("fault_tolerance", "未知")
            can_execute = execution_check.get("can_execute", False)
            
            print(f"   🔧 容错率: {fault_tolerance}")
            print(f"   🚀 可执行: {'✅' if can_execute else '❌'}")
            
            if fault_tolerance == "低" and not can_execute:
                unconfirmed = execution_check.get("unconfirmed_params", [])
                print(f"   ⚠️  需要确认: {unconfirmed}")
            
            print_parameter_states(manager, scenario['function'])
        
        if i < len(scenarios):
            input(f"\n按回车查看下一个场景...")

def main():
    """主函数"""
    print("🎯 打车系统参数验证演示")
    print("展示4状态参数验证：未填写 -> 未校验 -> 已校验待确认 -> 已确认")
    print("确保容错率低的参数都经过用户确认")
    
    # 检查环境变量
    if not os.getenv("AMAP_API_KEY"):
        print("\n⚠️  警告: 未设置AMAP_API_KEY，地理位置验证功能受限")
    
    print("\n选择演示模式:")
    print("1. 完整参数验证流程 (推荐)")
    print("2. 不同场景对比")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            demonstrate_parameter_flow()
            break
        elif choice == "2":
            demonstrate_different_scenarios()
            break
        elif choice == "3":
            print("👋 演示结束")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    main()
