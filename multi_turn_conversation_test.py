#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多轮对话测试脚本
测试 taxi_agent_system 的多轮对话能力，包括：
1. 完整的对话流程
2. 上下文记忆
3. 参数确认
4. 错误处理和恢复
5. 会话管理
6. 性能监控
"""

import time
import json
import random
from typing import List, Dict
from taxi_agent_system import MainAgent


class ConversationTester:
    """多轮对话测试器"""
    
    def __init__(self):
        self.agent = MainAgent()
        self.test_results = []
        self.session_counter = 0
    
    def generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        self.session_counter += 1
        session_id = f"test_session_{self.session_counter}_{int(time.time())}"
        return self.agent.start_new_session(session_id)
    
    def run_conversation_scenario(self, scenario: Dict) -> Dict:
        """运行单个对话场景"""
        session_id = self.generate_session_id()
        scenario_name = scenario.get("name", "未命名场景")
        turns = scenario.get("turns", [])
        
        print(f"\n{'='*60}")
        print(f"场景: {scenario_name}")
        print(f"会话ID: {session_id}")
        print(f"{'='*60}")
        
        results = {
            "scenario_name": scenario_name,
            "session_id": session_id,
            "turns": [],
            "success": True,
            "error_count": 0,
            "total_time": 0,
            "avg_response_time": 0
        }
        
        start_time = time.time()
        response_times = []
        
        for turn_num, user_input in enumerate(turns, 1):
            print(f"\n第{turn_num}轮:")
            print(f"用户: {user_input}")
            
            turn_start = time.time()
            
            try:
                response = self.agent.process_user_input(user_input, session_id)
                turn_time = time.time() - turn_start
                response_times.append(turn_time)
                
                print(f"助手: {response}")
                print(f"响应时间: {turn_time:.2f}秒")
                
                results["turns"].append({
                    "turn": turn_num,
                    "user_input": user_input,
                    "response": response,
                    "response_time": turn_time,
                    "success": True
                })
                
            except Exception as e:
                turn_time = time.time() - turn_start
                error_msg = str(e)
                
                print(f"错误: {error_msg}")
                print(f"错误发生时间: {turn_time:.2f}秒")
                
                results["turns"].append({
                    "turn": turn_num,
                    "user_input": user_input,
                    "error": error_msg,
                    "response_time": turn_time,
                    "success": False
                })
                
                results["error_count"] += 1
                results["success"] = False
            
            print("-" * 40)
            
            # 添加短暂延迟模拟真实对话节奏
            time.sleep(0.3)
        
        # 计算统计信息
        results["total_time"] = time.time() - start_time
        if response_times:
            results["avg_response_time"] = sum(response_times) / len(response_times)
        
        print(f"\n场景 '{scenario_name}' 完成")
        print(f"总耗时: {results['total_time']:.2f}秒")
        print(f"平均响应时间: {results['avg_response_time']:.2f}秒")
        print(f"错误次数: {results['error_count']}")
        
        self.test_results.append(results)
        return results


def get_test_scenarios() -> List[Dict]:
    """获取测试场景列表"""
    return [
        {
            "name": "完整打车流程",
            "description": "测试从需求表达到订单确认的完整流程",
            "turns": [
                "我想打车",
                "从康德大厦出发",
                "去大悦城",
                "要舒适型车辆",
                "确认订单"
            ]
        },
        {
            "name": "地点查询和导航",
            "description": "测试地点查询、路线规划和费用估算",
            "turns": [
                "西湖在哪里？",
                "从西湖到杭州东站怎么走？",
                "大概需要多长时间？",
                "打车费用大概多少？",
                "有更便宜的路线吗？"
            ]
        },
        {
            "name": "POI搜索和推荐",
            "description": "测试兴趣点搜索和相似推荐功能",
            "turns": [
                "北京有哪些星巴克？",
                "推荐上地附近的咖啡店",
                "哪个离地铁站最近？",
                "帮我查一下具体地址",
                "推荐附近的餐厅"
            ]
        },
        {
            "name": "错误处理和重试",
            "description": "测试系统对错误输入的处理和恢复能力",
            "turns": [
                "我要去火星",
                "我要去北京",
                "从天安门广场",
                "到鸟巢体育场",
                "现在就要出发"
            ]
        },
        {
            "name": "参数确认流程",
            "description": "测试低容错率功能的参数确认机制",
            "turns": [
                "帮我叫车",
                "从北京大学",
                "到清华大学",
                "要豪华型车辆",
                "确认起点",
                "确认终点",
                "确认车型",
                "开始叫车"
            ]
        },
        {
            "name": "上下文记忆测试",
            "description": "测试系统对对话上下文的记忆和理解",
            "turns": [
                "我想去西湖",
                "那里怎么样？",
                "从那里到杭州东站怎么走？",
                "打车要多少钱？",
                "好的，帮我叫车",
                "取消订单"
            ]
        },
        {
            "name": "模糊输入处理",
            "description": "测试对模糊或不完整输入的处理",
            "turns": [
                "我要去大厦",
                "康德大厦",
                "去商场",
                "大悦城",
                "确认"
            ]
        },
        {
            "name": "多功能混合使用",
            "description": "测试多种功能的混合使用场景",
            "turns": [
                "附近有什么好吃的？",
                "推荐一家川菜馆",
                "从我这里过去怎么走？",
                "打车要多少钱？",
                "帮我叫车过去",
                "顺便查一下那里的营业时间"
            ]
        }
    ]


def run_stress_test(tester: ConversationTester, num_sessions: int = 5):
    """运行压力测试"""
    print(f"\n{'='*60}")
    print(f"压力测试 - {num_sessions}个并发会话")
    print(f"{'='*60}")
    
    # 简单的测试请求
    test_requests = [
        "西湖在哪里？",
        "北京有哪些星巴克？",
        "从天安门到鸟巢怎么走？",
        "帮我叫车",
        "取消订单"
    ]
    
    start_time = time.time()
    
    for i in range(num_sessions):
        session_id = tester.generate_session_id()
        print(f"\n--- 会话 {i+1} (ID: {session_id}) ---")
        
        # 随机选择几个请求
        selected_requests = random.sample(test_requests, 3)
        
        for request in selected_requests:
            print(f"用户: {request}")
            try:
                response = tester.agent.process_user_input(request, session_id)
                print(f"助手: {response}")
            except Exception as e:
                print(f"错误: {e}")
    
    total_time = time.time() - start_time
    print(f"\n压力测试完成，总耗时: {total_time:.2f}秒")


def generate_test_report(test_results: List[Dict]):
    """生成测试报告"""
    print(f"\n{'='*60}")
    print("测试报告")
    print(f"{'='*60}")
    
    total_scenarios = len(test_results)
    successful_scenarios = sum(1 for r in test_results if r["success"])
    total_turns = sum(len(r["turns"]) for r in test_results)
    total_errors = sum(r["error_count"] for r in test_results)
    
    print(f"总场景数: {total_scenarios}")
    print(f"成功场景: {successful_scenarios}")
    print(f"成功率: {successful_scenarios/total_scenarios*100:.1f}%")
    print(f"总对话轮数: {total_turns}")
    print(f"总错误数: {total_errors}")
    print(f"错误率: {total_errors/total_turns*100:.1f}%")
    
    # 响应时间统计
    all_response_times = []
    for result in test_results:
        for turn in result["turns"]:
            if turn.get("response_time"):
                all_response_times.append(turn["response_time"])
    
    if all_response_times:
        avg_time = sum(all_response_times) / len(all_response_times)
        min_time = min(all_response_times)
        max_time = max(all_response_times)
        
        print(f"\n响应时间统计:")
        print(f"平均响应时间: {avg_time:.2f}秒")
        print(f"最快响应: {min_time:.2f}秒")
        print(f"最慢响应: {max_time:.2f}秒")
    
    # 详细场景结果
    print(f"\n详细场景结果:")
    for result in test_results:
        status = "✅ 成功" if result["success"] else "❌ 失败"
        print(f"- {result['scenario_name']}: {status} "
              f"(耗时: {result['total_time']:.1f}s, "
              f"错误: {result['error_count']})")


def main():
    """主测试函数"""
    print("多轮对话测试开始...")
    
    tester = ConversationTester()
    scenarios = get_test_scenarios()
    
    # 运行所有测试场景
    for scenario in scenarios:
        tester.run_conversation_scenario(scenario)
        time.sleep(1)  # 场景间隔
    
    # 运行压力测试
    run_stress_test(tester, num_sessions=3)
    
    # 生成测试报告
    generate_test_report(tester.test_results)
    
    print("\n所有测试完成！")


if __name__ == "__main__":
    main()
