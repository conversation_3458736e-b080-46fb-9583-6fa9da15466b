#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式多轮对话测试脚本
提供一个简单的命令行界面来测试 taxi_agent_system 的多轮对话能力
"""

import time
import json
from datetime import datetime
from taxi_agent_system import MainAgent


class InteractiveChatTester:
    """交互式聊天测试器"""
    
    def __init__(self):
        self.agent = MainAgent()
        self.session_id = self.agent.start_new_session(f"interactive_{int(time.time())}")
        self.conversation_history = []
        self.start_time = datetime.now()
        
    def print_welcome(self):
        """打印欢迎信息"""
        print("="*60)
        print("🚗 智能出行助手 - 多轮对话测试")
        print("="*60)
        print(f"会话ID: {self.session_id}")
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n可用命令:")
        print("  /help    - 显示帮助信息")
        print("  /history - 显示对话历史")
        print("  /clear   - 清空对话历史")
        print("  /stats   - 显示统计信息")
        print("  /test    - 运行预设测试场景")
        print("  /quit    - 退出程序")
        print("\n开始对话吧！输入您的问题...")
        print("-"*60)
    
    def print_help(self):
        """打印帮助信息"""
        print("\n📖 帮助信息:")
        print("这是一个智能出行助手，支持以下功能：")
        print("1. 地点查询 - 例如：'西湖在哪里？'")
        print("2. POI搜索 - 例如：'北京有哪些星巴克？'")
        print("3. 路线规划 - 例如：'从天安门到鸟巢怎么走？'")
        print("4. 打车服务 - 例如：'我要从康德大厦打车到大悦城'")
        print("5. 价格估算 - 例如：'打车要多少钱？'")
        print("6. 上车点推荐 - 例如：'附近哪里好打车？'")
        print("\n💡 提示：")
        print("- 支持多轮对话，系统会记住上下文")
        print("- 对于打车等重要操作，系统会要求确认")
        print("- 如果信息不完整，系统会主动询问")
    
    def show_conversation_history(self):
        """显示对话历史"""
        print(f"\n📜 对话历史 (共{len(self.conversation_history)}轮):")
        if not self.conversation_history:
            print("暂无对话记录")
            return
        
        for i, turn in enumerate(self.conversation_history, 1):
            print(f"\n第{i}轮 ({turn['timestamp']}):")
            print(f"👤 用户: {turn['user_input']}")
            print(f"🤖 助手: {turn['response']}")
            if turn.get('response_time'):
                print(f"⏱️  响应时间: {turn['response_time']:.2f}秒")
            if turn.get('error'):
                print(f"❌ 错误: {turn['error']}")
    
    def show_statistics(self):
        """显示统计信息"""
        print(f"\n📊 会话统计:")
        print(f"会话ID: {self.session_id}")
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        total_turns = len(self.conversation_history)
        successful_turns = sum(1 for turn in self.conversation_history if not turn.get('error'))
        error_turns = total_turns - successful_turns
        
        print(f"总对话轮数: {total_turns}")
        print(f"成功轮数: {successful_turns}")
        print(f"错误轮数: {error_turns}")
        
        if total_turns > 0:
            success_rate = successful_turns / total_turns * 100
            print(f"成功率: {success_rate:.1f}%")
            
            # 响应时间统计
            response_times = [turn['response_time'] for turn in self.conversation_history 
                            if turn.get('response_time')]
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                min_time = min(response_times)
                max_time = max(response_times)
                print(f"平均响应时间: {avg_time:.2f}秒")
                print(f"最快响应: {min_time:.2f}秒")
                print(f"最慢响应: {max_time:.2f}秒")
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        # 重新生成session_id
        self.session_id = self.agent.start_new_session(f"interactive_{int(time.time())}")
        print(f"✅ 对话历史已清空，新会话ID: {self.session_id}")
    
    def run_preset_tests(self):
        """运行预设测试场景"""
        print("\n🧪 运行预设测试场景...")
        
        test_scenarios = [
            {
                "name": "基础查询测试",
                "inputs": ["西湖在哪里？", "那里有什么好玩的？"]
            },
            {
                "name": "打车流程测试", 
                "inputs": ["我要打车", "从康德大厦", "到大悦城", "确认"]
            },
            {
                "name": "POI搜索测试",
                "inputs": ["北京有哪些星巴克？", "推荐上地附近的咖啡店"]
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n--- {scenario['name']} ---")
            for user_input in scenario['inputs']:
                print(f"👤 用户: {user_input}")
                response, response_time, error = self.process_input(user_input)
                if error:
                    print(f"❌ 错误: {error}")
                else:
                    print(f"🤖 助手: {response}")
                print(f"⏱️  响应时间: {response_time:.2f}秒")
                time.sleep(0.5)  # 短暂延迟
        
        print("\n✅ 预设测试完成")
    
    def process_input(self, user_input: str):
        """处理用户输入"""
        start_time = time.time()
        error = None
        response = ""
        
        try:
            response = self.agent.process_user_input(user_input, self.session_id)
        except Exception as e:
            error = str(e)
            response = f"抱歉，处理您的请求时出现错误: {error}"
        
        response_time = time.time() - start_time
        
        # 记录到历史
        self.conversation_history.append({
            "timestamp": datetime.now().strftime('%H:%M:%S'),
            "user_input": user_input,
            "response": response,
            "response_time": response_time,
            "error": error
        })
        
        return response, response_time, error
    
    def run(self):
        """运行交互式测试"""
        self.print_welcome()
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理命令
                if user_input.startswith('/'):
                    command = user_input[1:].lower()
                    
                    if command == 'help':
                        self.print_help()
                    elif command == 'history':
                        self.show_conversation_history()
                    elif command == 'clear':
                        self.clear_history()
                    elif command == 'stats':
                        self.show_statistics()
                    elif command == 'test':
                        self.run_preset_tests()
                    elif command == 'quit':
                        print("\n👋 再见！感谢使用智能出行助手。")
                        break
                    else:
                        print(f"❓ 未知命令: {command}，输入 /help 查看可用命令")
                    
                    continue
                
                # 处理正常对话
                print("🤖 助手正在思考...")
                response, response_time, error = self.process_input(user_input)
                
                if error:
                    print(f"❌ 错误: {error}")
                else:
                    print(f"🤖 助手: {response}")
                
                print(f"⏱️  响应时间: {response_time:.2f}秒")
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 系统错误: {e}")
                print("请重试或输入 /quit 退出")
        
        # 显示最终统计
        print("\n" + "="*60)
        print("📊 最终统计:")
        self.show_statistics()
        print("="*60)


def main():
    """主函数"""
    tester = InteractiveChatTester()
    tester.run()


if __name__ == "__main__":
    main()
