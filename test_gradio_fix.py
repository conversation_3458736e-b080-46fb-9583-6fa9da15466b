#!/usr/bin/env python3
"""
测试Gradio修复的脚本
验证修复后的gradio_demo.py是否能正常运行
"""

import sys
import subprocess
import time
import requests
from pathlib import Path

def test_gradio_import():
    """测试Gradio导入"""
    print("🔍 测试Gradio导入...")
    try:
        import gradio as gr
        print(f"✅ Gradio版本: {gr.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Gradio导入失败: {e}")
        return False

def test_gradio_syntax():
    """测试gradio_demo.py语法"""
    print("🔍 测试gradio_demo.py语法...")
    try:
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', 'gradio_demo.py'],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ gradio_demo.py语法检查通过")
            return True
        else:
            print(f"❌ 语法错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_gradio_startup():
    """测试Gradio启动"""
    print("🔍 测试Gradio启动...")
    
    if not Path('gradio_demo.py').exists():
        print("❌ gradio_demo.py文件不存在")
        return False
    
    try:
        # 启动Gradio进程
        process = subprocess.Popen(
            [sys.executable, 'gradio_demo.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        print("⏳ 等待Gradio启动...")
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ Gradio进程正在运行")
            
            # 尝试访问健康检查端点
            try:
                response = requests.get('http://localhost:7860', timeout=5)
                if response.status_code == 200:
                    print("✅ Gradio界面可访问")
                    success = True
                else:
                    print(f"⚠️  Gradio界面返回状态码: {response.status_code}")
                    success = True  # 进程运行就算成功
            except requests.RequestException:
                print("⚠️  无法访问Gradio界面，但进程正在运行")
                success = True
            
            # 停止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return success
        else:
            # 进程已退出
            stdout, stderr = process.communicate()
            print("❌ Gradio启动失败")
            if stderr:
                print(f"错误信息: {stderr}")
            if stdout:
                print(f"输出信息: {stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 测试启动失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试Gradio修复...")
    print("=" * 50)
    
    tests = [
        ("Gradio导入测试", test_gradio_import),
        ("语法检查测试", test_gradio_syntax),
        ("启动测试", test_gradio_startup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！Gradio修复成功！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return 1

if __name__ == '__main__':
    sys.exit(main())
