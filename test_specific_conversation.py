#!/usr/bin/env python3
"""
测试指定的对话用例：
用户: 打车
助手: 好啊，你要去哪？
用户: 去机场  
助手: 大兴还是北京国际机场？
用户: 大兴

验证参数验证和补全的完整流程
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_conversation_step(step_num, user_input, expected_response, description):
    """打印对话步骤信息"""
    print(f"\n{'='*80}")
    print(f"  第{step_num}步对话")
    print(f"{'='*80}")
    print(f"👤 用户: {user_input}")
    print(f"🎯 预期助手回复: {expected_response}")
    print(f"📝 验证目标: {description}")
    print(f"{'='*80}")

def analyze_response_match(actual_response, expected_keywords):
    """分析回复是否符合预期"""
    matched_keywords = []
    for keyword in expected_keywords:
        if keyword in actual_response:
            matched_keywords.append(keyword)
    
    match_rate = len(matched_keywords) / len(expected_keywords) if expected_keywords else 0
    
    print(f"\n📊 回复分析:")
    print(f"   实际回复: {actual_response}")
    print(f"   期望关键词: {expected_keywords}")
    print(f"   匹配关键词: {matched_keywords}")
    print(f"   匹配率: {match_rate:.1%}")
    
    if match_rate >= 0.5:
        print(f"   ✅ 回复符合预期")
    else:
        print(f"   ⚠️  回复可能不符合预期")
    
    return match_rate

def test_specific_conversation():
    """测试指定的对话用例"""
    print("🎯 测试指定对话用例")
    print("验证参数从缺失到补全的完整流程")
    
    # 初始化Agent
    print("\n🚀 初始化MainAgent...")
    agent = MainAgent()
    session_id = f"specific_test_{datetime.now().strftime('%H%M%S')}"
    
    # 定义对话步骤
    conversation_steps = [
        {
            "user_input": "帮我打车去海淀公园",
            "expected_response": "好啊，你要去哪？",
            "expected_keywords": ["去哪", "哪里", "目的地", "要去"],
            "description": "用户发起打车请求，系统应询问目的地",
            "parameter_state": "start_place和end_place都未填写"
        },
        {
            "user_input": "好的",
            "expected_response": "大兴还是首都国际机场？",
            "expected_keywords": ["大兴", "首都", "国际", "机场", "哪个"],
            "description": "用户说去机场，系统应澄清具体机场",
            "parameter_state": "end_place模糊(困惑度高)，需要澄清"
        },
        {
            "user_input": "改成去五道口吧",
            "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
            "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
            "description": "用户明确首都机场，系统应验证参数并请求确认",
            "parameter_state": "参数验证通过，显示确认方案"
        },
        {
            "user_input": "五道口附件有好吃的么？",
            "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
            "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
            "description": "用户明确首都机场，系统应验证参数并请求确认",
            "parameter_state": "参数验证通过，显示确认方案"
        },
        {
            "user_input": "我想想",
            "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
            "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
            "description": "用户明确首都机场，系统应验证参数并请求确认",
            "parameter_state": "参数验证通过，显示确认方案"
        },


        # {
        #     "user_input": "2号航站楼",
        #     "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
        #     "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
        #     "description": "用户明确首都机场，系统应验证参数并请求确认",
        #     "parameter_state": "参数验证通过，显示确认方案"
        # },
        # {
        #     "user_input": "晚上9点钟的飞机，来得及么，来得及的话就打吧，不行就取消",
        #     "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
        #     "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
        #     "description": "用户明确首都机场，系统应验证参数并请求确认",
        #     "parameter_state": "参数验证通过，显示确认方案"
        # }
        # {
        #     "user_input": "终点改成T3",
        #     "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
        #     "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
        #     "description": "用户明确首都机场，系统应验证参数并请求确认",
        #     "parameter_state": "参数验证通过，显示确认方案"
        # },
        # {
        #     "user_input": "起点改成大悦城",
        #     "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
        #     "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
        #     "description": "用户明确首都机场，系统应验证参数并请求确认",
        #     "parameter_state": "参数验证通过，显示确认方案"
        # },
        # {
        #     "user_input": "京西",
        #     "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
        #     "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
        #     "description": "用户明确首都机场，系统应验证参数并请求确认",
        #     "parameter_state": "参数验证通过，显示确认方案"
        # },
        {
            "user_input": "好的",
            "expected_response": "从上地五街方正大厦到首都机场，预计60分钟，120元可以吗？",
            "expected_keywords": ["方正大厦", "首都机场", "预计", "分钟", "元", "可以"],
            "description": "用户明确首都机场，系统应验证参数并请求确认",
            "parameter_state": "参数验证通过，显示确认方案"
        }
    ]
    
    # 执行对话测试
    for i, step in enumerate(conversation_steps, 1):
        print_conversation_step(
            i, 
            step["user_input"], 
            step["expected_response"], 
            step["description"]
        )
        
        print(f"📋 预期参数状态: {step['parameter_state']}")
        
        try:
            # 处理用户输入
            print(f"\n🔄 处理用户输入: '{step['user_input']}'")
            response = agent.process_user_input(step['user_input'], session_id)
            print("response:",response)
            # 分析回复匹配度
            match_rate = analyze_response_match(response, step["expected_keywords"])
            
            # 显示详细的function calling信息（如果有的话）
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    latest_action = hist[-1]
                    print(f"\n📋 最新动作状态:")
                    print(f"   可执行: {'✅' if latest_action.get('can_execute') else '❌'}")
                    
                    if not latest_action.get('can_execute'):
                        action_type = latest_action.get('action_type', '')
                        need_action = latest_action.get('need_user_action', False)
                        
                        if need_action:
                            print(f"   需要用户操作: {action_type}")
                            
                            if action_type == "provide_parameters":
                                missing = latest_action.get('missing_parameters', [])
                                print(f"   缺少参数: {missing}")
                            elif action_type == "fix_parameters":
                                errors = latest_action.get('validation_errors', [])
                                print(f"   参数错误: {errors}")
                            elif action_type == "confirm_with_candidates":
                                candidate_info = latest_action.get('candidate_info', {})
                                print(f"   候选信息: {len(candidate_info)}个参数有候选")
            
            # 评估这一步的成功程度
            print(f"\n🎯 第{i}步评估:")
            if match_rate >= 0.7:
                print(f"   ✅ 优秀 - 回复高度符合预期")
            elif match_rate >= 0.5:
                print(f"   ✅ 良好 - 回复基本符合预期")
            elif match_rate >= 0.3:
                print(f"   ⚠️  一般 - 回复部分符合预期")
            else:
                print(f"   ❌ 需要改进 - 回复不符合预期")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'.'*80}")
        if i < len(conversation_steps):
            input("按回车继续下一步对话...")
    
    # 总结测试结果
    print(f"\n🎉 对话测试完成！")
    print(f"\n📊 测试总结:")
    print(f"   1. ✅ 验证了参数从缺失到补全的完整流程")
    print(f"   2. ✅ 测试了困惑度检查机制（机场 → 大兴机场）")
    print(f"   3. ✅ 验证了系统的澄清和确认能力")
    print(f"   4. ✅ 检查了function calling的详细信息展示")

def test_parameter_evolution():
    """测试参数状态的演变过程"""
    print("\n" + "🔬" + "="*78)
    print("  参数状态演变分析")
    print("🔬" + "="*78)
    
    print("📋 分析目标:")
    print("   1. 观察参数从'未填写'到'已确认'的状态变化")
    print("   2. 验证困惑度检查对参数验证的影响")
    print("   3. 分析候选信息的获取和展示")
    
    agent = MainAgent()
    session_id = f"evolution_test_{datetime.now().strftime('%H%M%S')}"
    
    # 模拟参数状态演变
    evolution_steps = [
        {
            "step": "初始状态",
            "description": "用户说'打车'，所有参数未填写",
            "expected_state": {
                "start_place": "未填写",
                "end_place": "未填写"
            }
        },
        {
            "step": "部分填写",
            "description": "用户说'去机场'，end_place模糊",
            "expected_state": {
                "start_place": "可能从环境推断",
                "end_place": "模糊(困惑度>0.2)"
            }
        },
        {
            "step": "澄清完成",
            "description": "用户说'大兴'，参数明确",
            "expected_state": {
                "start_place": "方正大厦(从环境获取)",
                "end_place": "大兴机场(困惑度≤0.2)"
            }
        }
    ]
    
    inputs = ["打车", "去机场", "大兴"]
    
    for i, (input_text, evolution) in enumerate(zip(inputs, evolution_steps)):
        print(f"\n📊 {evolution['step']}")
        print(f"   输入: '{input_text}'")
        print(f"   描述: {evolution['description']}")
        print(f"   预期状态: {evolution['expected_state']}")
        
        try:
            response = agent.process_user_input(input_text, session_id)
            print(f"   实际回复: {response}")
            
            # 分析参数管理器状态（如果可用）
            if hasattr(agent.enhanced_agent, 'parameter_manager'):
                manager = agent.enhanced_agent.parameter_manager
                if "call_taxi_service" in manager.function_parameters:
                    status = manager.get_function_status("call_taxi_service")
                    if status.get("status"):
                        parameters = status.get("parameters", {})
                        print(f"   参数状态:")
                        for param_name, param_info in parameters.items():
                            state = param_info.get("state", "未知")
                            value = param_info.get("value", "无")
                            print(f"     {param_name}: {state} (值: {value})")
            
        except Exception as e:
            print(f"   ❌ 处理出错: {str(e)}")
        
        if i < len(inputs) - 1:
            input("\n按回车继续下一个演变步骤...")

def main():
    """主函数"""
    print("🎯 指定对话用例测试")
    print("测试用例：用户: 打车 → 去机场 → 大兴")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. 完整对话流程测试 (推荐)")
    print("2. 参数状态演变分析")
    print("3. 两个测试都执行")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            test_specific_conversation()
            break
        elif choice == "2":
            test_parameter_evolution()
            break
        elif choice == "3":
            test_specific_conversation()
            test_parameter_evolution()
            break
        elif choice == "4":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-4")

if __name__ == "__main__":
    main()
