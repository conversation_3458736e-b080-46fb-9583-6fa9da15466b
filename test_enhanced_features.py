#!/usr/bin/env python3
"""
测试增强功能的脚本
包括新的function calling、参数验证、确认流程等
"""

import os
import sys
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from taxi_agent_system import EnhancedTaxiAgent
from parameter_validation import FunctionParameterManager, ParameterState
from amap_mcp_tools import mcp_search_taxi_spots, mcp_estimate_taxi_price


def test_new_functions():
    """测试新增的函数功能"""
    print("=== 测试新增函数功能 ===")
    
    # 测试上车点推荐
    print("\n1. 测试上车点推荐功能")
    try:
        result = mcp_search_taxi_spots("北京大学", "北京", 1000)
        print(f"上车点推荐结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"上车点推荐测试失败: {e}")
    
    # 测试价格估算
    print("\n2. 测试价格估算功能")
    try:
        result = mcp_estimate_taxi_price("北京天安门", "北京西站", car_type="经济型")
        print(f"价格估算结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"价格估算测试失败: {e}")


def test_parameter_validation():
    """测试参数验证系统"""
    print("\n=== 测试参数验证系统 ===")
    
    try:
        # 创建参数管理器
        param_manager = FunctionParameterManager()
        
        # 测试低容错率函数的参数管理
        function_name = "call_taxi_service"
        provided_params = {
            "start_place": "方正大厦",
            "end_place": "机场"
        }
        
        print(f"\n1. 初始化函数参数: {function_name}")
        init_result = param_manager.initialize_function_parameters(function_name, provided_params)
        print(f"初始化结果: {json.dumps(init_result, ensure_ascii=False, indent=2)}")
        
        print(f"\n2. 验证所有参数")
        validation_result = param_manager.validate_all_parameters(function_name)
        print(f"验证结果: {json.dumps(validation_result, ensure_ascii=False, indent=2)}")
        
        print(f"\n3. 检查执行条件")
        execution_check = param_manager.can_execute_function(function_name)
        print(f"执行检查: {json.dumps(execution_check, ensure_ascii=False, indent=2)}")
        
        print(f"\n4. 获取函数状态")
        status = param_manager.get_function_status(function_name)
        print(f"函数状态: {json.dumps(status, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"参数验证测试失败: {e}")


def test_enhanced_agent():
    """测试增强版Agent"""
    print("\n=== 测试增强版Agent ===")
    
    try:
        agent = EnhancedTaxiAgent()
        
        # 测试用例
        test_cases = [
            "帮我找一下北京大学附近的上车点",
            "估算一下从天安门到北京西站的打车费用",
            "我要从方正大厦打车到机场",
            "确认起点参数：方正大厦",
            "取消终点参数"
        ]
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n{i}. 测试输入: {test_input}")
            try:
                response = agent.process_message(test_input, f"test_session_{i}")
                print(f"   回复: {response}")
            except Exception as e:
                print(f"   错误: {e}")
                
    except Exception as e:
        print(f"增强版Agent测试失败: {e}")


def test_parameter_confirmation_workflow():
    """测试参数确认工作流"""
    print("\n=== 测试参数确认工作流 ===")
    
    try:
        param_manager = FunctionParameterManager()
        
        # 模拟低容错率函数的完整流程
        function_name = "call_taxi_service"
        
        # 步骤1: 初始化参数
        print("步骤1: 初始化参数")
        provided_params = {"start_place": "方正大厦", "end_place": "大悦城"}
        init_result = param_manager.initialize_function_parameters(function_name, provided_params)
        print(f"初始化: {init_result['status']}")
        
        # 步骤2: 验证参数
        print("\n步骤2: 验证参数")
        validation_result = param_manager.validate_all_parameters(function_name)
        print(f"验证: {validation_result['all_valid']}")
        
        # 步骤3: 检查执行条件
        print("\n步骤3: 检查执行条件")
        execution_check = param_manager.can_execute_function(function_name)
        print(f"可执行: {execution_check['can_execute']}")
        print(f"需要确认的参数: {execution_check.get('unconfirmed_params', [])}")
        
        # 步骤4: 确认参数
        if execution_check.get('unconfirmed_params'):
            print("\n步骤4: 确认参数")
            for param_name in execution_check['unconfirmed_params']:
                confirm_result = param_manager.confirm_parameter(function_name, param_name)
                print(f"确认 {param_name}: {confirm_result['status']}")
        
        # 步骤5: 再次检查执行条件
        print("\n步骤5: 再次检查执行条件")
        final_check = param_manager.can_execute_function(function_name)
        print(f"最终可执行: {final_check['can_execute']}")
        
        # 步骤6: 测试取消参数
        print("\n步骤6: 测试取消参数")
        cancel_result = param_manager.cancel_parameter(function_name, "start_place")
        print(f"取消起点: {cancel_result['status']}")
        
        # 步骤7: 检查取消后的状态
        print("\n步骤7: 检查取消后的状态")
        final_status = param_manager.get_function_status(function_name)
        print(f"最终状态: {json.dumps(final_status, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"参数确认工作流测试失败: {e}")


def main():
    """主测试函数"""
    print("开始测试增强功能...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境变量
    if not os.getenv("AMAP_API_KEY"):
        print("警告: 未设置AMAP_API_KEY环境变量，部分功能可能无法正常工作")
    
    if not os.getenv("BAILIAN_API_KEY"):
        print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能可能无法正常工作")
    
    # 运行测试
    try:
        test_new_functions()
        test_parameter_validation()
        test_parameter_confirmation_workflow()
        test_enhanced_agent()
        
        print("\n=== 测试完成 ===")
        print("所有测试已执行完毕，请查看上述输出结果")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
