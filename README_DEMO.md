# 🚗 智能打车助手演示系统

基于高德地图API和AI技术的现代化智能打车服务演示，提供多种交互界面供测试和演示使用。

## ✨ 功能特性

### 🎯 核心功能
- **🗺️ 地点查询**: 将地点名称转换为精确的经纬度坐标
- **🏙️ 城市信息**: 查询城市代码和相关信息
- **📍 POI搜索**: 搜索兴趣点和商户信息
- **🚗 智能打车**: 智能打车服务和价格估算

### 🖥️ 多种界面
1. **Streamlit Web界面** - 现代化的Web应用，功能最完整，推荐使用
2. **Gradio简洁界面** - 简洁易用的Web界面，适合快速测试
3. **React前端界面** - 响应式的现代化UI，支持实时聊天
4. **命令行界面** - 简单直接的终端交互
5. **REST API** - 标准的HTTP API接口

## 🚀 快速开始

### 🎯 一键启动（推荐）
```bash
# 自动安装依赖并启动所有界面
python demo_launcher.py
```

### 📋 手动启动步骤

#### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者手动安装核心包
pip install streamlit gradio flask flask-cors requests openai plotly pandas
```

#### 2. 设置环境变量
```bash
# 百炼AI API密钥（必需）
export BAILIAN_API_KEY="your_bailian_api_key"

# 高德地图API密钥（必需）
export AMAP_API_KEY="your_amap_api_key"
```

#### 3. 启动演示

**方式一：交互式启动脚本**
```bash
python start_demo.py
```

**方式二：直接启动特定界面**
```bash
# Streamlit界面
python start_demo.py --mode streamlit

# React界面
python start_demo.py --mode react

# Gradio界面
python start_demo.py --mode gradio

# 命令行界面
python start_demo.py --mode cli

# API服务器
python start_demo.py --mode api
```

**方式三：手动启动**
```bash
# Streamlit
streamlit run taxi_demo_app.py

# Gradio
python gradio_demo.py

# API服务器
python api_server.py

# 测试系统
python test_demo_system.py
```

## 📱 界面介绍

### 🌐 Streamlit Web界面
- **访问地址**: http://localhost:8501
- **特点**: 
  - 现代化的聊天界面
  - 实时系统状态监控
  - 性能指标可视化
  - 示例查询快速测试
  - 对话历史管理

### ⚛️ React前端界面
- **访问地址**: http://localhost:3000
- **特点**:
  - 响应式设计，支持移动端
  - 流畅的动画效果
  - 实时消息传递
  - 现代化UI组件
  - 系统状态实时显示

### 🎨 Gradio简洁界面
- **访问地址**: http://localhost:7860
- **特点**:
  - 简洁易用的Web界面
  - 快速测试和演示
  - 支持会话重置功能
  - 实时系统状态显示

### 🖥️ 命令行界面
- **特点**:
  - 简单直接的文本交互
  - 适合开发和调试
  - 支持批量测试
  - 轻量级，无需浏览器

### 🔧 REST API
- **基础地址**: http://localhost:8000
- **主要端点**:
  - `POST /api/chat` - 发送消息
  - `POST /api/sessions/new` - 创建新会话
  - `POST /api/sessions/{id}/reset` - 重置会话
  - `GET /api/sessions/{id}/info` - 获取会话信息
  - `GET /api/health` - 健康检查
  - `GET /api/system/stats` - 系统统计

## � 会话管理功能

### ✨ 新增功能
- **会话隔离**: 每个用户拥有独立的对话上下文
- **会话重置**: 支持清空对话历史，重新开始
- **多用户支持**: 支持多个用户同时使用，互不干扰
- **会话状态查询**: 可查看会话信息和消息统计

### 🎯 使用场景
1. **Web应用**: 为每个用户创建独立会话
2. **测试环境**: 快速重置会话状态
3. **演示展示**: 清空历史记录重新演示
4. **多用户服务**: 支持并发用户访问

### 📱 界面支持
- **React界面**: 提供会话重置按钮，显示会话ID
- **Gradio界面**: 支持会话管理和状态显示
- **API接口**: 完整的会话管理REST API
- **Streamlit界面**: 集成会话控制功能

## �💡 使用示例

### 示例查询
```
1. "西湖在哪里？"
   - 测试地点查询功能

2. "帮我查一下杭州的城市代码"
   - 测试城市信息查询

3. "北京有哪些星巴克？"
   - 测试POI搜索功能

4. "我要从康德大厦打车到太阳宫"
   - 测试基础打车服务

5. "从北京站到首都机场，要舒适型车辆"
   - 测试高级打车服务
```

### API调用示例

```bash
# 创建新会话
curl -X POST http://localhost:8000/api/sessions/new \
  -H "Content-Type: application/json" \
  -d '{"session_id": "my_session"}'

# 发送聊天消息
curl -X POST http://localhost:8000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "西湖在哪里？",
    "session_id": "my_session"
  }'

# 重置会话（清空对话历史）
curl -X POST http://localhost:8000/api/sessions/my_session/reset

# 获取会话信息
curl http://localhost:8000/api/sessions/my_session/info

# 健康检查
curl http://localhost:8000/api/health

# 获取系统统计
curl http://localhost:8000/api/system/stats
```

## 🏗️ 项目结构

```
taxi_demo/
├── taxi_agent_system.py      # 核心打车系统
├── amap_mcp_tools.py         # 高德地图工具
├── taxi_demo_app.py          # Streamlit应用
├── api_server.py             # Flask API服务器
├── start_demo.py             # 启动脚本
├── react_chat_demo/          # React前端
│   ├── src/
│   │   ├── App.js
│   │   └── components/
│   └── package.json
└── README_DEMO.md            # 本文档
```

## 🔧 配置说明

### 环境变量
| 变量名 | 必需 | 说明 |
|--------|------|------|
| `BAILIAN_API_KEY` | ✅ | 百炼AI API密钥 |
| `AMAP_API_KEY` | ✅ | 高德地图API密钥 |

### 端口配置
| 服务 | 默认端口 | 说明 |
|------|----------|------|
| Streamlit | 8501 | Web界面 |
| React | 3000 | 前端开发服务器 |
| API服务器 | 8000 | REST API |

## 🐛 故障排除

### 常见问题

1. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

2. **环境变量未设置**
   ```bash
   # 检查环境变量
   echo $BAILIAN_API_KEY
   echo $AMAP_API_KEY
   ```

3. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8501
   lsof -i :8000
   lsof -i :3000
   ```

4. **React依赖安装失败**
   ```bash
   cd react_chat_demo
   rm -rf node_modules package-lock.json
   npm install
   ```

### 调试模式

启用详细日志：
```bash
export PYTHONPATH=.
export LOG_LEVEL=DEBUG
python start_demo.py --mode streamlit
```

## 📊 性能监控

### 系统指标
- 响应时间统计
- 成功率监控
- 错误日志记录
- 会话状态跟踪

### 监控界面
- Streamlit界面提供实时性能图表
- API提供JSON格式的统计数据
- 支持自定义监控集成

## 🤝 开发指南

### 添加新功能
1. 在 `taxi_agent_system.py` 中添加新的工具函数
2. 更新系统提示词和工具定义
3. 在前端界面中添加相应的UI组件
4. 更新API文档和示例

### 自定义界面
1. 参考现有的Streamlit和React实现
2. 使用标准的REST API接口
3. 遵循响应式设计原则
4. 添加适当的错误处理

## 📄 许可证

本项目仅供演示和学习使用。

## 🙋‍♂️ 支持

如有问题或建议，请：
1. 查看故障排除部分
2. 检查日志输出
3. 提交Issue或联系开发团队
