# 会话管理 API 使用说明

## 🎯 核心功能

现在 `MainAgent` 支持完整的会话管理功能，可以：
- 创建独立的用户会话
- 重新开始对话（清空上下文）
- 查询会话状态信息
- 支持多用户并发使用

## 📚 API 接口

### 1. 创建新会话
```python
# 自动生成会话ID
session_id = agent.start_new_session()

# 指定会话ID
session_id = agent.start_new_session("my_custom_session")

# 重启现有会话（清空上下文）
new_session_id = agent.start_new_session("existing_session_id")
```

### 2. 处理用户输入
```python
# 使用默认会话
response = agent.process_user_input("你好")

# 使用指定会话
response = agent.process_user_input("你好", session_id)
```

### 3. 查询会话信息
```python
info = agent.get_session_info(session_id)
# 返回: {
#     "session_id": "session_123",
#     "has_context": True,
#     "message_count": 5,
#     "last_activity": None
# }
```

## 🚀 使用示例

### 基础使用
```python
from taxi_agent_system import MainAgent

# 创建智能助手
agent = MainAgent()

# 创建新会话
session_id = agent.start_new_session("user_alice")

# 进行对话
response1 = agent.process_user_input("我想去西湖", session_id)
response2 = agent.process_user_input("那里怎么样？", session_id)  # 理解"那里"指西湖
```

### 多用户场景
```python
# 为不同用户创建独立会话
alice_session = agent.start_new_session("alice")
bob_session = agent.start_new_session("bob")

# Alice的对话
agent.process_user_input("我想去西湖", alice_session)
agent.process_user_input("那里怎么样？", alice_session)

# Bob的对话（完全独立）
agent.process_user_input("找星巴克", bob_session)
agent.process_user_input("那里怎么走？", bob_session)  # 不会与Alice的"那里"混淆
```

### 重新开始对话
```python
# 用户进行了一些对话
agent.process_user_input("我想去西湖", session_id)
agent.process_user_input("那里怎么样？", session_id)

# 用户想重新开始
new_session_id = agent.start_new_session(session_id)  # 清空上下文

# 重新开始后
agent.process_user_input("那里怎么样？", new_session_id)  # 不再理解"那里"
```

### Web应用集成
```python
# 用户登录时
def on_user_login(user_id):
    session_id = agent.start_new_session(f"web_user_{user_id}")
    return session_id

# 处理用户消息
def handle_user_message(user_message, session_id):
    response = agent.process_user_input(user_message, session_id)
    return response

# 重新开始对话
def restart_conversation(session_id):
    new_session_id = agent.start_new_session(session_id)
    return new_session_id
```

## 🔧 最佳实践

### 会话命名规范
```python
# 用户会话
session_id = agent.start_new_session(f"user_{user_id}")

# 临时会话
session_id = agent.start_new_session(f"temp_{timestamp}")

# 测试会话
session_id = agent.start_new_session(f"test_{test_name}")
```

### 会话生命周期管理
```python
# 检查会话状态
info = agent.get_session_info(session_id)

# 如果消息过多，考虑重启会话
if info['message_count'] > 50:
    new_session_id = agent.start_new_session(session_id)
    
# 定期清理不活跃的会话
if not info['has_context']:
    # 会话可能已经不活跃
    pass
```

### 错误处理
```python
try:
    response = agent.process_user_input(user_message, session_id)
except Exception as e:
    # 如果会话出错，可以尝试重启
    new_session_id = agent.start_new_session(session_id)
    response = "抱歉，系统重启了会话，请重新开始对话。"
```

## 🧪 测试脚本

### 运行会话管理测试
```bash
# 完整的会话管理功能测试
python session_management_test.py

# 使用示例和最佳实践
python session_example.py

# 快速功能测试（包含会话管理）
python quick_test.py
```

### 测试覆盖的功能
- ✅ 会话创建和管理
- ✅ 会话隔离和独立性  
- ✅ 会话重启和上下文清理
- ✅ 上下文连续性
- ✅ 并发会话处理

## 📊 性能特点

- **内存效率**: 每个会话独立管理上下文
- **并发支持**: 支持多用户同时使用
- **状态隔离**: 不同会话完全隔离
- **灵活重启**: 可随时清空会话上下文

## 🔄 向后兼容

原有的 API 调用方式仍然有效：
```python
# 这种调用方式仍然可用（使用默认会话）
response = agent.process_user_input("你好")

# 等同于
response = agent.process_user_input("你好", "default")
```

## 💡 使用建议

1. **为每个用户创建独立会话**，避免用户间的对话混淆
2. **定期重启长时间的会话**，防止上下文过长影响性能
3. **使用有意义的会话ID**，便于调试和管理
4. **在Web应用中结合用户ID和时间戳**创建唯一会话ID
5. **处理异常时考虑重启会话**，提供更好的用户体验

---

通过这些会话管理功能，您的智能助手现在可以：
- 支持多用户并发使用
- 提供"重新开始对话"功能
- 维护独立的用户上下文
- 更好地集成到Web应用中
