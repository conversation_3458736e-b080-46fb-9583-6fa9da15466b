#!/usr/bin/env python3
"""
测试EnhancedTaxiAgent.process_message集成的脚本
验证所有演示系统是否正确使用最新接口
"""

import sys
import os
import time
from datetime import datetime

def test_taxi_agent_import():
    """测试EnhancedTaxiAgent导入"""
    print("🔍 测试EnhancedTaxiAgent导入...")
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        agent = EnhancedTaxiAgent()
        print("✅ EnhancedTaxiAgent导入成功")
        
        # 测试process_message方法
        if hasattr(agent, 'process_message'):
            print("✅ process_message方法存在")
        else:
            print("❌ process_message方法不存在")
            return False
            
        return True
    except ImportError as e:
        print(f"❌ EnhancedTaxiAgent导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ EnhancedTaxiAgent初始化失败: {e}")
        return False

def test_session_management():
    """测试会话管理功能"""
    print("\n🔍 测试会话管理功能...")
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        agent = EnhancedTaxiAgent()
        
        # 测试基本对话
        session_id = "test_session_1"
        response1 = agent.process_message("你好", session_id)
        print(f"✅ 基本对话测试通过: {response1[:50]}...")
        
        # 测试会话隔离
        session_id2 = "test_session_2"
        response2 = agent.process_message("我想去西湖", session_id2)
        print(f"✅ 会话隔离测试通过")
        
        # 测试上下文记忆
        response3 = agent.process_message("那里怎么样？", session_id2)
        print(f"✅ 上下文记忆测试通过")
        
        # 测试会话重置（通过删除上下文模拟）
        if hasattr(agent, 'context') and session_id2 in agent.context:
            del agent.context[session_id2]
            print("✅ 会话重置功能可用")
        
        return True
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
        return False

def test_gradio_integration():
    """测试Gradio集成"""
    print("\n🔍 测试Gradio集成...")
    try:
        # 检查gradio_demo.py语法
        import subprocess
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', 'gradio_demo.py'],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ gradio_demo.py语法检查通过")
        else:
            print(f"❌ gradio_demo.py语法错误: {result.stderr}")
            return False
        
        # 检查是否使用了EnhancedTaxiAgent
        with open('gradio_demo.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'EnhancedTaxiAgent' in content and 'process_message' in content:
                print("✅ Gradio使用EnhancedTaxiAgent.process_message")
            else:
                print("❌ Gradio未正确使用EnhancedTaxiAgent.process_message")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Gradio集成测试失败: {e}")
        return False

def test_api_server_integration():
    """测试API服务器集成"""
    print("\n🔍 测试API服务器集成...")
    try:
        # 检查api_server.py语法
        import subprocess
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', 'api_server.py'],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ api_server.py语法检查通过")
        else:
            print(f"❌ api_server.py语法错误: {result.stderr}")
            return False
        
        # 检查是否使用了EnhancedTaxiAgent
        with open('api_server.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'EnhancedTaxiAgent' in content and 'process_message' in content:
                print("✅ API服务器使用EnhancedTaxiAgent.process_message")
            else:
                print("❌ API服务器未正确使用EnhancedTaxiAgent.process_message")
                return False
        
        # 检查新的session管理API
        if '/api/sessions/new' in content and '/api/sessions/<session_id>/reset' in content:
            print("✅ API服务器包含session管理端点")
        else:
            print("❌ API服务器缺少session管理端点")
            return False
        
        return True
    except Exception as e:
        print(f"❌ API服务器集成测试失败: {e}")
        return False

def test_streamlit_integration():
    """测试Streamlit集成"""
    print("\n🔍 测试Streamlit集成...")
    try:
        # 检查taxi_demo_app.py语法
        import subprocess
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', 'taxi_demo_app.py'],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ taxi_demo_app.py语法检查通过")
        else:
            print(f"❌ taxi_demo_app.py语法错误: {result.stderr}")
            return False
        
        # 检查是否使用了EnhancedTaxiAgent
        with open('taxi_demo_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'EnhancedTaxiAgent' in content and 'process_message' in content:
                print("✅ Streamlit使用EnhancedTaxiAgent.process_message")
            else:
                print("❌ Streamlit未正确使用EnhancedTaxiAgent.process_message")
                return False
        
        # 检查会话重置功能
        if '重置会话' in content and 'session_id' in content:
            print("✅ Streamlit包含会话重置功能")
        else:
            print("❌ Streamlit缺少会话重置功能")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Streamlit集成测试失败: {e}")
        return False

def test_react_integration():
    """测试React集成"""
    print("\n🔍 测试React集成...")
    try:
        # 检查React演示是否存在
        if os.path.exists('react_chat_demo/src/App.js'):
            with open('react_chat_demo/src/App.js', 'r', encoding='utf-8') as f:
                content = f.read()
                if 'session_id' in content and 'resetSession' in content:
                    print("✅ React包含会话重置功能")
                else:
                    print("⚠️  React可能缺少会话重置功能")
                    return True  # 不算失败，因为React是独立的前端
        else:
            print("⚠️  React演示目录不存在")
            return True  # 不算失败
        
        return True
    except Exception as e:
        print(f"❌ React集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试EnhancedTaxiAgent.process_message集成")
    print("=" * 60)
    
    tests = [
        ("EnhancedTaxiAgent导入测试", test_taxi_agent_import),
        ("会话管理功能测试", test_session_management),
        ("Gradio集成测试", test_gradio_integration),
        ("API服务器集成测试", test_api_server_integration),
        ("Streamlit集成测试", test_streamlit_integration),
        ("React集成测试", test_react_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！EnhancedTaxiAgent.process_message集成成功！")
        print("\n📝 集成完成的功能:")
        print("- ✅ 使用EnhancedTaxiAgent.process_message作为统一后端")
        print("- ✅ 支持会话管理和重置功能")
        print("- ✅ 所有演示界面已更新")
        print("- ✅ 新增session管理API端点")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return 1

if __name__ == '__main__':
    sys.exit(main())
