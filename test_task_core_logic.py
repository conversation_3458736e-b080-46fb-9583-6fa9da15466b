#!/usr/bin/env python3
"""
测试任务管理核心逻辑（不需要API密钥）
"""

import os
import sys
from datetime import datetime

# 设置虚拟环境变量以避免API调用
os.environ["AMAP_API_KEY"] = "test_key"
os.environ["BAILIAN_API_KEY"] = "test_key"

try:
    from taxi_agent_system import TaskInfo, TaskParameter, TaskParameterState
    print("✅ 成功导入任务管理数据结构")
    
    # 测试TaskParameterState枚举
    print(f"\n📊 测试TaskParameterState枚举:")
    for state in TaskParameterState:
        print(f"   {state.name}: {state.value}")
    
    # 测试TaskParameter数据类
    print(f"\n📝 测试TaskParameter数据类:")
    param = TaskParameter(
        name="start_place",
        value="方正大厦",
        state=TaskParameterState.NOT_VALIDATED
    )
    print(f"   参数名: {param.name}")
    print(f"   参数值: {param.value}")
    print(f"   参数状态: {param.state.value}")
    
    # 测试TaskInfo数据类
    print(f"\n📋 测试TaskInfo数据类:")
    task_info = TaskInfo(
        task_id="test_task_001",
        function_name="call_taxi_service",
        parameters={"start_place": param},
        fault_tolerance="低",
        created_time=datetime.now().isoformat(),
        updated_time=datetime.now().isoformat()
    )
    print(f"   任务ID: {task_info.task_id}")
    print(f"   函数名: {task_info.function_name}")
    print(f"   容错率: {task_info.fault_tolerance}")
    print(f"   状态: {task_info.status}")
    
    # 测试EnhancedTaxiAgent的任务管理功能
    print(f"\n🤖 测试EnhancedTaxiAgent任务管理功能:")
    
    from taxi_agent_system import EnhancedTaxiAgent
    
    # 创建agent实例（不初始化API客户端）
    agent = EnhancedTaxiAgent()
    print("✅ 成功创建EnhancedTaxiAgent实例")
    
    # 测试创建任务
    print(f"\n🔄 测试创建任务:")
    task_id = agent.create_task(
        "call_taxi_service",
        {"start_place": "方正大厦", "end_place": None},
        "test_session"
    )
    print(f"✅ 创建任务成功: {task_id}")
    
    # 查看待确认任务列表
    print(f"\n📋 查看待确认任务列表:")
    pending_tasks = agent.get_pending_tasks()
    print(f"待确认任务数量: {len(pending_tasks)}")
    
    for tid, task_info in pending_tasks.items():
        print(f"   任务: {tid}")
        print(f"   函数: {task_info['function_name']}")
        print(f"   容错率: {task_info['fault_tolerance']}")
        print(f"   可执行: {task_info['executable']}")
        
        for param_name, param_info in task_info['parameters'].items():
            state_icon = {
                "未填写": "⚪",
                "未校验": "🟡", 
                "已校验待确认": "🟠",
                "已确认": "🟢"
            }.get(param_info['state'], "❓")
            
            print(f"     {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")
    
    # 测试更新参数
    print(f"\n🔄 测试更新参数:")
    success = agent.update_task_parameter(task_id, "end_place", "大兴机场")
    print(f"更新end_place为'大兴机场': {'✅ 成功' if success else '❌ 失败'}")
    
    # 再次查看任务状态
    print(f"\n📊 更新后的任务状态:")
    pending_tasks = agent.get_pending_tasks()
    task_info = pending_tasks[task_id]
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡", 
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")
    
    # 测试确认参数
    print(f"\n✅ 测试确认参数:")
    success1 = agent.confirm_parameter(task_id, "start_place")
    print(f"确认start_place: {'✅ 成功' if success1 else '❌ 失败'}")
    
    success2 = agent.confirm_parameter(task_id, "end_place")
    print(f"确认end_place: {'✅ 成功' if success2 else '❌ 失败'}")
    
    # 检查任务可执行性
    print(f"\n🚀 检查任务可执行性:")
    executable_check = agent.check_task_executable(task_id)
    print(f"可执行: {'✅' if executable_check['can_execute'] else '❌'}")
    print(f"容错率: {executable_check['fault_tolerance']}")
    
    if not executable_check['can_execute']:
        if executable_check['missing_params']:
            print(f"缺少参数: {executable_check['missing_params']}")
        if executable_check['unvalidated_params']:
            print(f"未校验参数: {executable_check['unvalidated_params']}")
        if executable_check['unconfirmed_params']:
            print(f"未确认参数: {executable_check['unconfirmed_params']}")
    
    # 测试取消参数
    print(f"\n❌ 测试取消参数:")
    success = agent.cancel_parameter(task_id, "end_place")
    print(f"取消end_place: {'✅ 成功' if success else '❌ 失败'}")
    
    # 查看取消后的状态
    print(f"\n📊 取消后的任务状态:")
    pending_tasks = agent.get_pending_tasks()
    task_info = pending_tasks[task_id]
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡", 
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")
    
    # 测试检查可执行任务
    print(f"\n🔍 测试检查可执行任务:")
    executable_tasks = agent.check_executable_tasks()
    print(f"可执行任务列表: {executable_tasks}")
    
    # 查看已执行任务列表
    print(f"\n✅ 查看已执行任务列表:")
    executed_tasks = agent.get_executed_tasks()
    print(f"已执行任务数量: {len(executed_tasks)}")
    
    print(f"\n🎉 任务管理核心逻辑测试完成！")
    print(f"\n📋 测试总结:")
    print(f"   ✅ 任务创建功能正常")
    print(f"   ✅ 参数状态管理正常")
    print(f"   ✅ 参数确认功能正常")
    print(f"   ✅ 参数取消功能正常")
    print(f"   ✅ 任务可执行性检查正常")
    print(f"   ✅ 待确认任务列表管理正常")
    print(f"   ✅ 已执行任务列表管理正常")

    # 模拟用户输入序列测试
    print(f"\n" + "="*60)
    print(f"🎭 模拟用户输入序列测试")
    print(f"用户输入序列: '我要打车从方正大厦出发' → '去机场' → '大兴' → '好的'")
    print(f"="*60)

    # 重新创建agent实例进行序列测试
    agent_seq = EnhancedTaxiAgent()

    # 第1轮: "我要打车从方正大厦出发"
    print(f"\n🔄 第1轮输入: '我要打车从方正大厦出发'")
    task_id_seq = agent_seq.create_task(
        "call_taxi_service",
        {"start_place": "方正大厦", "end_place": None},
        "test_session_seq"
    )

    # 显示当前任务状态
    pending_tasks = agent_seq.get_pending_tasks()
    task_info = pending_tasks[task_id_seq]
    print(f"📊 当前任务状态:")
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡",
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")

    # 第2轮: "去机场"
    print(f"\n🔄 第2轮输入: '去机场'")
    agent_seq.update_task_parameter(task_id_seq, "end_place", "机场")

    # 显示更新后状态
    pending_tasks = agent_seq.get_pending_tasks()
    task_info = pending_tasks[task_id_seq]
    print(f"📊 更新后任务状态:")
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡",
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")

    # 第3轮: "大兴" (澄清机场具体位置)
    print(f"\n🔄 第3轮输入: '大兴' (澄清机场位置)")
    # 使用智能澄清功能：当用户提供澄清信息时，自动验证并确认
    agent_seq.update_task_parameter(task_id_seq, "end_place", "大兴机场",
                                   {"is_valid": True, "confusion_score": 0.05},
                                   is_clarification=True)

    # 显示澄清后状态
    pending_tasks = agent_seq.get_pending_tasks()
    task_info = pending_tasks[task_id_seq]
    print(f"📊 澄清后任务状态:")
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡",
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")

    # 第4轮: "好的" (用户确认)
    print(f"\n🔄 第4轮输入: '好的' (用户确认)")

    # 模拟start_place参数验证通过（实际应该通过POI搜索验证）
    agent_seq.update_task_parameter(task_id_seq, "start_place", "方正大厦",
                                   {"is_valid": True, "confusion_score": 0.1})

    # 用户确认start_place参数（end_place已在第3轮确认）
    agent_seq.confirm_parameter(task_id_seq, "start_place")

    # 显示最终状态
    pending_tasks = agent_seq.get_pending_tasks()
    task_info = pending_tasks[task_id_seq]
    print(f"📊 最终任务状态:")
    for param_name, param_info in task_info['parameters'].items():
        state_icon = {
            "未填写": "⚪",
            "未校验": "🟡",
            "已校验待确认": "🟠",
            "已确认": "🟢"
        }.get(param_info['state'], "❓")
        print(f"   {state_icon} {param_name}: {param_info['value']} ({param_info['state']})")

    # 检查任务可执行性
    executable_check = agent_seq.check_task_executable(task_id_seq)
    print(f"\n🚀 任务可执行性检查:")
    print(f"   可执行: {'✅' if executable_check['can_execute'] else '❌'}")
    print(f"   容错率: {executable_check['fault_tolerance']}")

    if executable_check['can_execute']:
        print(f"   🎉 任务已准备就绪，可以执行打车服务！")
    else:
        if executable_check['missing_params']:
            print(f"   缺少参数: {executable_check['missing_params']}")
        if executable_check['unvalidated_params']:
            print(f"   未校验参数: {executable_check['unvalidated_params']}")
        if executable_check['unconfirmed_params']:
            print(f"   未确认参数: {executable_check['unconfirmed_params']}")

    print(f"\n🎭 用户输入序列测试完成！")
    print(f"✅ 成功模拟了完整的用户交互流程")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 测试出错: {e}")
    import traceback
    traceback.print_exc()
