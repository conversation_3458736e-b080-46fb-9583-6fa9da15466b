#!/usr/bin/env python3
"""
测试POI搜索结果的困惑度计算功能
"""

import os
from amap_mcp_tools import AmapMCPTools, mcp_search_poi, mcp_search_poi_with_segmentation

def test_confusion_calculation():
    """测试困惑度计算"""
    print("=== 测试困惑度计算功能 ===")
    
    test_cases = [
        {
            "keyword": "方正大厦",
            "city": "北京市",
            "description": "具体建筑物搜索",
            "expected": "应该有较低的困惑度（同一区域的相关建筑）"
        },
        {
            "keyword": "星巴克",
            "city": "北京市", 
            "description": "连锁品牌搜索",
            "expected": "可能有中等困惑度（分布在不同区域）"
        },
        {
            "keyword": "医院",
            "city": "北京市",
            "description": "通用类型搜索",
            "expected": "可能有较高困惑度（分布很广）"
        },
        {
            "keyword": "海淀医院",
            "city": "北京市",
            "description": "特定区域医院",
            "expected": "应该有较低困惑度（集中在海淀区）"
        }
    ]
    
    for case in test_cases:
        print(f"🔍 测试: {case['keyword']}")
        print(f"📝 描述: {case['description']}")
        print(f"🎯 期望: {case['expected']}")
        
        result = mcp_search_poi(case['keyword'], case['city'])
        
        if result.get("status"):
            data = result["data"]
            seg_info = data.get("segmentation_info", {})
            
            confusion_score = seg_info.get("confusion_score", 0)
            confusion_level = seg_info.get("confusion_level", "未知")
            
            print(f"📊 结果:")
            print(f"   分词: {seg_info.get('segments', [])}")
            print(f"   POI数量: {data['count']} 个")
            print(f"   困惑度分数: {confusion_score:.3f}")
            print(f"   困惑度等级: {confusion_level}")
            
            # 显示前3个POI的位置信息
            print(f"   前3个POI位置:")
            for i, poi in enumerate(data["pois"][:3], 1):
                if poi.get('longitude') and poi.get('latitude'):
                    print(f"   {i}. {poi['name']}")
                    print(f"      坐标: ({poi['latitude']:.6f}, {poi['longitude']:.6f})")
                    print(f"      地址: {poi['address']}")
                else:
                    print(f"   {i}. {poi['name']} (无坐标信息)")
            
            # 分析困惑度
            if confusion_score <= 0.2:
                analysis = "✅ 很好！POI集中在附近区域，用户选择明确"
            elif confusion_score <= 0.4:
                analysis = "👍 不错，POI相对集中，困惑度较低"
            elif confusion_score <= 0.6:
                analysis = "⚠️ 中等困惑，POI分布较散，可能需要更多信息"
            elif confusion_score <= 0.8:
                analysis = "❗ 较高困惑，POI分布很散，建议细化搜索"
            else:
                analysis = "🚨 高度困惑，POI分布极散，强烈建议细化搜索条件"
            
            print(f"   分析: {analysis}")
            
        else:
            print(f"❌ 搜索失败: {result.get('error')}")
        
        print()

def test_distance_calculation():
    """测试距离计算功能"""
    print("=== 测试距离计算功能 ===")
    
    amap = AmapMCPTools()
    
    # 测试已知距离的坐标点
    test_points = [
        {
            "name": "天安门 vs 故宫",
            "point1": (39.90923, 116.397428),  # 天安门
            "point2": (39.916668, 116.397026), # 故宫
            "expected": "约800米"
        },
        {
            "name": "北京 vs 上海",
            "point1": (39.90923, 116.397428),  # 北京天安门
            "point2": (31.230416, 121.473701), # 上海外滩
            "expected": "约1200公里"
        },
        {
            "name": "相同位置",
            "point1": (39.90923, 116.397428),
            "point2": (39.90923, 116.397428),
            "expected": "0米"
        }
    ]
    
    for test in test_points:
        lat1, lng1 = test["point1"]
        lat2, lng2 = test["point2"]
        
        distance = amap._calculate_distance(lat1, lng1, lat2, lng2)
        
        print(f"📏 {test['name']}:")
        print(f"   计算距离: {distance:.0f}米 ({distance/1000:.1f}公里)")
        print(f"   预期距离: {test['expected']}")
        print()

def test_confusion_levels():
    """测试不同困惑度等级"""
    print("=== 测试困惑度等级分类 ===")
    
    amap = AmapMCPTools()
    
    test_scores = [0.0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.0]
    
    for score in test_scores:
        level = amap._get_confusion_level(score)
        
        if score <= 0.2:
            description = "300米以内，非常清晰"
        elif score <= 0.4:
            description = "300-1000米，比较清晰"
        elif score <= 0.6:
            description = "1000-2000米，有一定困惑"
        elif score <= 0.8:
            description = "2000-3000米，比较困惑"
        else:
            description = "3000米以上，高度困惑"
        
        print(f"困惑度分数: {score:.1f} → 等级: {level} ({description})")

def test_real_scenarios():
    """测试真实场景的困惑度"""
    print("\n=== 真实场景困惑度测试 ===")
    
    scenarios = [
        {
            "keyword": "中关村软件园",
            "city": "北京市",
            "scenario": "特定园区搜索",
            "expectation": "低困惑度"
        },
        {
            "keyword": "万达广场",
            "city": "北京市",
            "scenario": "连锁商场搜索",
            "expectation": "中等困惑度"
        },
        {
            "keyword": "银行",
            "city": "北京市",
            "scenario": "通用服务搜索",
            "expectation": "高困惑度"
        }
    ]
    
    for scenario in scenarios:
        print(f"🎬 场景: {scenario['scenario']}")
        print(f"🔍 搜索: {scenario['keyword']}")
        print(f"🎯 预期: {scenario['expectation']}")
        
        result = mcp_search_poi(scenario['keyword'], scenario['city'])
        
        if result.get("status"):
            seg_info = result["data"].get("segmentation_info", {})
            confusion_score = seg_info.get("confusion_score", 0)
            confusion_level = seg_info.get("confusion_level", "未知")
            
            print(f"📊 实际结果:")
            print(f"   困惑度: {confusion_score:.3f} ({confusion_level})")
            print(f"   POI数量: {result['data']['count']} 个")
            
            # 验证预期
            if scenario['expectation'] == "低困惑度" and confusion_score <= 0.4:
                print("   ✅ 符合预期")
            elif scenario['expectation'] == "中等困惑度" and 0.3 <= confusion_score <= 0.7:
                print("   ✅ 符合预期")
            elif scenario['expectation'] == "高困惑度" and confusion_score >= 0.6:
                print("   ✅ 符合预期")
            else:
                print("   ⚠️ 与预期有差异")
        else:
            print(f"   ❌ 搜索失败")
        
        print()

def main():
    """主测试函数"""
    print("🧭 POI搜索困惑度计算功能测试")
    print("=" * 60)
    print("💡 困惑度基于top3 POI之间的距离计算")
    print("📏 300米以内为正常可接受范围（困惑度≤0.2）")
    print()
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请设置环境变量 AMAP_API_KEY")
        return
    
    try:
        # 基础功能测试
        test_confusion_calculation()
        
        # 距离计算测试
        test_distance_calculation()
        
        # 困惑度等级测试
        test_confusion_levels()
        
        # 真实场景测试
        test_real_scenarios()
        
        print("✅ 所有测试完成")
        print()
        print("📝 困惑度计算规则总结:")
        print("   • 0.0-0.2: 很低 (300米以内，正常可接受)")
        print("   • 0.2-0.4: 低 (300-1000米，比较清晰)")
        print("   • 0.4-0.6: 中等 (1000-2000米，有一定困惑)")
        print("   • 0.6-0.8: 高 (2000-3000米，比较困惑)")
        print("   • 0.8-1.0: 很高 (3000米以上，高度困惑)")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
