#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示版多轮对话测试脚本
使用模拟数据演示多轮对话功能，无需真实API密钥
"""

import time
import random
from typing import Dict, List


class MockAgent:
    """模拟的智能助手，用于演示多轮对话"""
    
    def __init__(self):
        self.conversation_history = {}
        self.current_context = {}
        
        # 模拟响应模板
        self.responses = {
            "greeting": [
                "你好！我是智能出行助手，有什么可以帮到你？",
                "您好！需要什么帮助吗？",
                "欢迎使用智能出行助手！"
            ],
            "location_query": [
                "西湖位于浙江省杭州市，是著名的旅游景点。",
                "北京天安门位于北京市东城区，是中国的象征性建筑。",
                "上海外滩位于黄浦江西岸，是上海的标志性景观。"
            ],
            "poi_search": [
                "找到了20家星巴克门店，最近的是王府井店。",
                "附近有5家麦当劳，推荐建国门店。",
                "搜索到15家便利店，7-11最近。"
            ],
            "taxi_request": [
                "好的，请告诉我您的出发地和目的地。",
                "我来帮您叫车，需要确认起点和终点。",
                "正在为您安排车辆，请稍等。"
            ],
            "route_planning": [
                "从天安门到鸟巢大约15公里，预计30分钟车程。",
                "建议路线：经三环路，避开拥堵路段。",
                "打车费用预计35-45元。"
            ],
            "confirmation": [
                "请确认：从康德大厦到大悦城，舒适型车辆？",
                "确认信息：起点北京大学，终点清华大学？",
                "订单确认：经济型车辆，预计费用25元？"
            ]
        }
    
    def process_user_input(self, user_input: str, session_id: str = "default") -> str:
        """处理用户输入并返回响应"""
        
        # 初始化会话
        if session_id not in self.conversation_history:
            self.conversation_history[session_id] = []
            self.current_context[session_id] = {}
        
        # 记录用户输入
        self.conversation_history[session_id].append({
            "role": "user",
            "content": user_input,
            "timestamp": time.time()
        })
        
        # 模拟处理延迟
        time.sleep(random.uniform(0.5, 2.0))
        
        # 生成响应
        response = self._generate_response(user_input, session_id)
        
        # 记录助手响应
        self.conversation_history[session_id].append({
            "role": "assistant", 
            "content": response,
            "timestamp": time.time()
        })
        
        return response
    
    def _generate_response(self, user_input: str, session_id: str) -> str:
        """根据用户输入生成响应"""
        user_input_lower = user_input.lower()
        
        # 问候
        if any(word in user_input_lower for word in ["你好", "hello", "hi"]):
            return random.choice(self.responses["greeting"])
        
        # 地点查询
        elif any(word in user_input_lower for word in ["在哪里", "位置", "地址"]):
            return random.choice(self.responses["location_query"])
        
        # POI搜索
        elif any(word in user_input_lower for word in ["星巴克", "麦当劳", "便利店", "有哪些"]):
            return random.choice(self.responses["poi_search"])
        
        # 打车请求
        elif any(word in user_input_lower for word in ["打车", "叫车", "出租车"]):
            # 更新上下文
            self.current_context[session_id]["service"] = "taxi"
            return random.choice(self.responses["taxi_request"])
        
        # 路线规划
        elif any(word in user_input_lower for word in ["怎么走", "路线", "距离", "时间"]):
            return random.choice(self.responses["route_planning"])
        
        # 确认操作
        elif any(word in user_input_lower for word in ["确认", "好的", "是的"]):
            return random.choice(self.responses["confirmation"])
        
        # 上下文相关回复
        elif "那里" in user_input_lower or "那个" in user_input_lower:
            return "根据之前的对话，您是指刚才提到的地点吗？我可以为您提供更多信息。"
        
        # 默认回复
        else:
            return "我理解您的需求，让我来帮您处理。请提供更多具体信息。"


def demo_conversation_scenarios():
    """演示多轮对话场景"""
    
    print("🎭 多轮对话演示开始")
    print("="*60)
    
    agent = MockAgent()
    
    scenarios = [
        {
            "name": "基础对话流程",
            "turns": [
                "你好",
                "西湖在哪里？",
                "那里怎么样？",
                "我想去那里"
            ]
        },
        {
            "name": "完整打车流程", 
            "turns": [
                "我要打车",
                "从康德大厦",
                "到大悦城",
                "要舒适型车辆",
                "确认订单"
            ]
        },
        {
            "name": "POI搜索和推荐",
            "turns": [
                "北京有哪些星巴克？",
                "推荐最近的一家",
                "那里怎么走？",
                "打车要多少钱？"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景 {i}: {scenario['name']}")
        print("-" * 40)
        
        session_id = f"demo_session_{i}"
        
        for turn_num, user_input in enumerate(scenario['turns'], 1):
            print(f"\n第{turn_num}轮:")
            print(f"👤 用户: {user_input}")
            
            start_time = time.time()
            response = agent.process_user_input(user_input, session_id)
            response_time = time.time() - start_time
            
            print(f"🤖 助手: {response}")
            print(f"⏱️  响应时间: {response_time:.2f}秒")
        
        print(f"\n✅ 场景 '{scenario['name']}' 完成")
        print("=" * 60)


def demo_context_memory():
    """演示上下文记忆功能"""
    
    print("\n🧠 上下文记忆演示")
    print("="*60)
    
    agent = MockAgent()
    session_id = "context_demo"
    
    context_flow = [
        "我想去西湖",
        "那里有什么好玩的？",  # 测试指代理解
        "从那里到杭州东站怎么走？",  # 测试上下文记忆
        "打车要多少钱？",  # 测试路线记忆
        "好的，帮我叫车"  # 测试完整信息记忆
    ]
    
    print("演示系统如何记住和理解对话上下文...")
    
    for turn, user_input in enumerate(context_flow, 1):
        print(f"\n第{turn}轮:")
        print(f"👤 用户: {user_input}")
        
        start_time = time.time()
        response = agent.process_user_input(user_input, session_id)
        response_time = time.time() - start_time
        
        print(f"🤖 助手: {response}")
        print(f"⏱️  响应时间: {response_time:.2f}秒")
        
        # 显示上下文分析
        if "那里" in user_input or "那个" in user_input:
            print("💡 系统分析: 检测到指代词，正在关联之前的对话内容")
    
    print("\n✅ 上下文记忆演示完成")


def demo_multi_session():
    """演示多会话管理"""
    
    print("\n👥 多会话管理演示")
    print("="*60)
    
    agent = MockAgent()
    
    # 模拟3个用户的并发会话
    sessions = [
        {"id": "user_alice", "name": "Alice", "requests": ["我在北京", "想去西湖", "怎么走？"]},
        {"id": "user_bob", "name": "Bob", "requests": ["找星巴克", "最近的在哪", "帮我叫车"]},
        {"id": "user_charlie", "name": "Charlie", "requests": ["我要打车", "从机场", "到市中心"]}
    ]
    
    print("演示系统如何同时处理多个用户的独立会话...")
    
    # 交替处理不同用户的请求
    max_turns = max(len(session["requests"]) for session in sessions)
    
    for turn in range(max_turns):
        print(f"\n--- 第{turn+1}轮交互 ---")
        
        for session in sessions:
            if turn < len(session["requests"]):
                user_name = session["name"]
                session_id = session["id"]
                user_input = session["requests"][turn]
                
                print(f"\n{user_name} ({session_id}):")
                print(f"👤 用户: {user_input}")
                
                start_time = time.time()
                response = agent.process_user_input(user_input, session_id)
                response_time = time.time() - start_time
                
                print(f"🤖 助手: {response}")
                print(f"⏱️  响应时间: {response_time:.2f}秒")
    
    print("\n✅ 多会话管理演示完成")
    print("💡 每个用户的会话都是独立的，不会相互干扰")


def demo_error_handling():
    """演示错误处理和恢复"""
    
    print("\n🚨 错误处理演示")
    print("="*60)
    
    agent = MockAgent()
    session_id = "error_demo"
    
    error_scenarios = [
        "我要去火星",  # 无效地点
        "从不存在的地方出发",  # 无效起点
        "到另一个不存在的地方",  # 无效终点
        "我要去北京",  # 恢复正常
        "从天安门到鸟巢"  # 正常请求
    ]
    
    print("演示系统如何处理错误输入并恢复正常...")
    
    for turn, user_input in enumerate(error_scenarios, 1):
        print(f"\n第{turn}轮:")
        print(f"👤 用户: {user_input}")
        
        try:
            start_time = time.time()
            response = agent.process_user_input(user_input, session_id)
            response_time = time.time() - start_time
            
            print(f"🤖 助手: {response}")
            print(f"⏱️  响应时间: {response_time:.2f}秒")
            
            # 模拟错误检测
            if "火星" in user_input or "不存在" in user_input:
                print("⚠️  系统检测: 输入包含无效信息，但系统仍能正常响应")
            else:
                print("✅ 系统状态: 正常处理")
                
        except Exception as e:
            print(f"❌ 系统错误: {e}")
            print("🔄 系统正在尝试恢复...")
    
    print("\n✅ 错误处理演示完成")


def main():
    """主演示函数"""
    
    print("🚀 智能出行助手 - 多轮对话演示")
    print("="*60)
    print("本演示展示了多轮对话系统的核心功能：")
    print("1. 基础对话流程")
    print("2. 上下文记忆")
    print("3. 多会话管理") 
    print("4. 错误处理和恢复")
    print("="*60)
    
    try:
        # 运行各项演示
        demo_conversation_scenarios()
        demo_context_memory()
        demo_multi_session()
        demo_error_handling()
        
        print("\n🎉 所有演示完成！")
        print("="*60)
        print("📊 演示总结:")
        print("- 展示了完整的多轮对话流程")
        print("- 验证了上下文记忆功能")
        print("- 测试了多会话并发处理")
        print("- 演示了错误处理机制")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
