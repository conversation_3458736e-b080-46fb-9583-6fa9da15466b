#!/usr/bin/env python3
"""
简化的任务管理系统测试
"""

import os
import sys

# 检查环境变量
required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
missing_vars = [var for var in required_vars if not os.getenv(var)]

if missing_vars:
    print(f"⚠️  缺少环境变量: {missing_vars}")
    print("请设置环境变量后再运行测试")
    sys.exit(1)

try:
    from taxi_agent_system import MainAgent
    print("✅ 成功导入MainAgent")
    
    # 创建agent实例
    agent = MainAgent()
    print("✅ 成功创建MainAgent实例")
    
    # 测试任务管理功能
    enhanced_agent = agent.enhanced_agent
    
    # 测试创建任务
    task_id = enhanced_agent.create_task(
        "call_taxi_service",
        {"start_place": "方正大厦", "end_place": None},
        "test_session"
    )
    print(f"✅ 成功创建任务: {task_id}")
    
    # 查看待确认任务列表
    pending_tasks = enhanced_agent.get_pending_tasks()
    print(f"📋 待确认任务数量: {len(pending_tasks)}")
    
    for task_id, task_info in pending_tasks.items():
        print(f"   任务: {task_id}")
        print(f"   函数: {task_info['function_name']}")
        print(f"   容错率: {task_info['fault_tolerance']}")
        print(f"   可执行: {task_info['executable']}")
        
        for param_name, param_info in task_info['parameters'].items():
            print(f"     参数 {param_name}: {param_info['value']} ({param_info['state']})")
    
    # 测试更新参数
    success = enhanced_agent.update_task_parameter(task_id, "end_place", "大兴机场")
    print(f"✅ 更新参数结果: {success}")
    
    # 测试确认参数
    success = enhanced_agent.confirm_parameter(task_id, "start_place")
    print(f"✅ 确认start_place: {success}")
    
    success = enhanced_agent.confirm_parameter(task_id, "end_place")
    print(f"✅ 确认end_place: {success}")
    
    # 检查任务是否可执行
    executable_check = enhanced_agent.check_task_executable(task_id)
    print(f"🚀 任务可执行性: {executable_check}")
    
    # 查看已执行任务列表
    executed_tasks = enhanced_agent.get_executed_tasks()
    print(f"✅ 已执行任务数量: {len(executed_tasks)}")
    
    print("\n🎉 任务管理系统基础功能测试通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试出错: {e}")
    import traceback
    traceback.print_exc()
