# 新函数验证逻辑说明

## 概述

根据需求，我们实现了基于函数类型的差异化验证策略，将函数分为两类：需要校验的函数和只需完备性检查的函数。

## 函数分类

### 1. 需要校验的函数（低容错率）
- **`call_taxi_service`** - 打车服务

**特点：**
- 涉及金钱交易，需要严格验证
- 地理位置参数需要POI校验
- 困惑度检查机制
- 候选信息提示
- 用户确认流程

### 2. 只需完备性检查的函数（高容错率）
- **`mcp_geocode_address`** - 地址转坐标
- **`mcp_search_poi`** - POI搜索
- **`mcp_calculate_poi_to_poi_route`** - 路线计算
- **其他查询类函数**

**特点：**
- 仅用于信息查询
- 只检查必填参数完整性
- 参数完整即可执行

## 验证流程

### 需要校验的函数流程

```
1. 检查必填参数完整性
   ↓
2. 对地理位置参数进行POI验证
   ↓
3. 困惑度检查
   ├─ ≤0.2: 验证通过 → 获取候选信息 → 用户确认
   └─ >0.2: 验证失败 → 要求用户澄清
   ↓
4. 执行函数
```

### 只需完备性检查的函数流程

```
1. 检查必填参数完整性
   ├─ 完整: 直接执行
   └─ 不完整: 要求补充参数
```

## 技术实现

### 1. 核心验证方法

```python
def _validate_function_by_type(self, function_name: str, function_args: dict, session_id: str) -> dict:
    """根据函数类型进行不同的验证策略"""
    validation_required_functions = ["call_taxi_service"]
    
    if function_name in validation_required_functions:
        # 需要校验的函数：完整的参数验证流程
        return self._validate_with_poi_verification(function_name, function_args, session_id)
    else:
        # 不需要校验的函数：只检查完备性
        return self._validate_completeness_only(function_name, function_args, session_id)
```

### 2. POI验证逻辑

```python
def _validate_location_with_poi(self, location: str) -> dict:
    """使用mcp_search_poi验证地理位置"""
    search_result = mcp_search_poi_with_segmentation(location)
    confusion_score = search_result.get("data", {}).get("confusion_score", 0.0)
    
    if confusion_score > 0.2:
        return {"is_valid": False, "confusion_score": confusion_score}
    else:
        return {"is_valid": True, "confusion_score": confusion_score}
```

### 3. 候选信息处理

```python
def _format_candidate_info_for_prompt(self, candidate_info: dict) -> str:
    """将候选信息格式化为prompt"""
    prompt_info = "\n\n📍 地理位置验证结果:"
    
    for param_name, info in candidate_info.items():
        candidates = info.get("candidates", [])
        if candidates:
            prompt_info += f"\n找到的候选地点:"
            for i, candidate in enumerate(candidates, 1):
                name = candidate.get("name", "未知")
                address = candidate.get("address", "未知地址")
                prompt_info += f"\n  {i}. {name} - {address}"
    
    return prompt_info
```

## 环境变量配置

### 新增城市环境变量

```python
DEFAULT_CITY = os.getenv("DEFAULT_CITY", "北京")  # 默认城市
```

**使用场景：**
- 地理位置解析的城市上下文
- 候选信息筛选
- 用户确认提示中的城市信息

### 环境变量设置

```bash
export DEFAULT_CITY="北京"
export DEFAULT_LONGITUDE="116.306345"
export DEFAULT_LATITUDE="40.040377"
export AMAP_API_KEY="your_amap_api_key"
export BAILIAN_API_KEY="your_bailian_api_key"
```

## 困惑度机制

### 困惑度阈值：0.2

- **≤0.2**：地理位置明确，验证通过
  - 获取候选信息
  - 加入prompt提示用户
  - 请求用户确认方案

- **>0.2**：地理位置模糊，验证失败
  - 要求用户提供更具体信息
  - 给出澄清建议

### 困惑度示例

| 地理位置 | 困惑度 | 处理结果 |
|---------|--------|----------|
| "北京大兴国际机场" | 0.05 | ✅ 通过，显示候选信息 |
| "方正大厦" | 0.15 | ✅ 通过，显示候选信息 |
| "机场" | 0.85 | ❌ 失败，要求澄清 |
| "火车站" | 0.90 | ❌ 失败，要求澄清 |

## 用户交互流程

### 场景1：困惑度低（≤0.2）

```
用户: 我要从方正大厦打车去大兴机场
系统: [POI验证] 困惑度检查通过
系统: [获取候选信息] 找到候选地点
系统: [加入prompt] 显示候选信息
助手: 好的，从方正大厦到大兴机场，预计60分钟，120元，确认吗？
```

### 场景2：困惑度高（>0.2）

```
用户: 我要打车去机场
系统: [POI验证] 困惑度过高(0.85)
系统: [验证失败] 要求澄清
助手: 请问是去哪个机场呢？大兴机场还是首都机场？
```

### 场景3：高容错率函数

```
用户: 搜索附近的星巴克
系统: [完备性检查] 参数完整
系统: [直接执行] 无需额外验证
助手: 找到附近3家星巴克：1. 星巴克(上地店)...
```

## 系统提示词优化

### 根据验证结果生成不同提示

```python
def _generate_system_prompt_for_response(self, validation_result: dict) -> str:
    if validation_result.get("action_type") == "confirm_with_candidates":
        return f"""
用户的地理位置参数验证通过（困惑度≤0.2），现在需要确认执行方案。

处理策略：
1. 根据候选地点信息，向用户确认具体的地理位置
2. 如果是打车服务，提供详细的路线和费用信息
3. 询问用户是否确认执行该方案

当前城市环境：{DEFAULT_CITY}
请结合候选信息和用户意图，生成确认性的回复。"""
```

## 测试验证

### 测试脚本

- `test_new_validation_logic.py` - 新验证逻辑测试
- `test_detailed_function_calls.py` - 详细function calling信息测试

### 测试用例

1. **打车服务POI验证测试**
   - 明确地理位置（困惑度低）
   - 模糊地理位置（困惑度高）
   - 候选信息展示

2. **其他函数完备性测试**
   - 参数完整性检查
   - 直接执行验证

3. **困惑度边界测试**
   - 0.2阈值边界情况
   - 不同困惑度的处理

## 总结

新的验证逻辑实现了：

1. ✅ **函数分类**：call_taxi_service需要校验，其他函数只需完备性检查
2. ✅ **POI校验**：使用mcp_search_poi验证地理位置参数
3. ✅ **困惑度机制**：0.2阈值判断，低困惑度获取候选信息
4. ✅ **候选信息提示**：将候选信息加入prompt，引导用户确认
5. ✅ **城市环境变量**：DEFAULT_CITY支持城市上下文
6. ✅ **差异化处理**：不同函数类型采用不同验证策略

这个实现确保了打车等敏感操作的安全性，同时保持了查询类操作的便利性。
