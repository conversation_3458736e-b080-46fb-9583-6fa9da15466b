#!/usr/bin/env python3
"""
演示POI搜索困惑度计算功能
展示如何通过距离分析来评估搜索结果的明确性
"""

import os
from amap_mcp_tools import mcp_search_poi

def demo_confusion_concept():
    """演示困惑度概念"""
    print("🧭 POI搜索困惑度功能演示")
    print("=" * 60)
    print("💡 困惑度概念:")
    print("   • 基于top3 POI之间的距离计算")
    print("   • 距离越近，困惑度越小（用户选择更明确）")
    print("   • 距离越远，困惑度越大（用户可能困惑）")
    print("   • 300米以内为正常可接受范围")
    print()
    
    print("📊 困惑度等级:")
    levels = [
        ("0.0-0.2", "很低", "300米以内", "✅ 非常清晰，用户选择明确"),
        ("0.2-0.4", "低", "300-1000米", "👍 比较清晰，轻微困惑"),
        ("0.4-0.6", "中等", "1000-2000米", "⚠️ 有一定困惑，可能需要更多信息"),
        ("0.6-0.8", "高", "2000-3000米", "❗ 比较困惑，建议细化搜索"),
        ("0.8-1.0", "很高", "3000米以上", "🚨 高度困惑，强烈建议细化搜索条件")
    ]
    
    for score_range, level, distance, description in levels:
        print(f"   {score_range}: {level:4s} ({distance:10s}) - {description}")
    
    print("\n" + "=" * 60 + "\n")

def demo_clear_search():
    """演示清晰搜索（低困惑度）"""
    print("✅ 清晰搜索示例 - 海淀医院")
    print("=" * 40)
    
    keyword = "海淀医院"
    city = "北京市"
    
    print(f"🔍 搜索: {keyword}")
    print(f"📍 城市: {city}")
    print()
    
    result = mcp_search_poi(keyword, city)
    
    if result.get("status"):
        data = result["data"]
        seg_info = data.get("segmentation_info", {})
        
        confusion_score = seg_info.get("confusion_score", 0)
        confusion_level = seg_info.get("confusion_level", "未知")
        
        print(f"📊 搜索结果:")
        print(f"   分词: {seg_info.get('segments', [])}")
        print(f"   POI数量: {data['count']} 个")
        print(f"   困惑度: {confusion_score:.3f} ({confusion_level})")
        print()
        
        print("🏥 前3个POI:")
        for i, poi in enumerate(data["pois"][:3], 1):
            print(f"   {i}. {poi['name']}")
            print(f"      地址: {poi['address']}")
            if poi.get('longitude') and poi.get('latitude'):
                print(f"      坐标: ({poi['latitude']:.6f}, {poi['longitude']:.6f})")
        
        print()
        print("💡 分析:")
        if confusion_score <= 0.2:
            print("   ✅ 搜索结果非常清晰！")
            print("   ✅ 所有POI都集中在附近区域")
            print("   ✅ 用户可以轻松做出选择")
        else:
            print("   ⚠️ 困惑度超出预期")
    
    print("\n" + "=" * 60 + "\n")

def demo_confusing_search():
    """演示困惑搜索（高困惑度）"""
    print("⚠️ 困惑搜索示例 - 万达广场")
    print("=" * 40)
    
    keyword = "万达广场"
    city = "北京市"
    
    print(f"🔍 搜索: {keyword}")
    print(f"📍 城市: {city}")
    print()
    
    result = mcp_search_poi(keyword, city)
    
    if result.get("status"):
        data = result["data"]
        seg_info = data.get("segmentation_info", {})
        
        confusion_score = seg_info.get("confusion_score", 0)
        confusion_level = seg_info.get("confusion_level", "未知")
        
        print(f"📊 搜索结果:")
        print(f"   分词: {seg_info.get('segments', [])}")
        print(f"   POI数量: {data['count']} 个")
        print(f"   困惑度: {confusion_score:.3f} ({confusion_level})")
        print()
        
        print("🏬 前3个POI:")
        for i, poi in enumerate(data["pois"][:3], 1):
            print(f"   {i}. {poi['name']}")
            print(f"      地址: {poi['address']}")
            if poi.get('longitude') and poi.get('latitude'):
                print(f"      坐标: ({poi['latitude']:.6f}, {poi['longitude']:.6f})")
        
        print()
        print("💡 分析:")
        if confusion_score >= 0.8:
            print("   🚨 高度困惑的搜索结果！")
            print("   📍 POI分布在北京各个区域")
            print("   💭 用户可能需要更具体的信息，如:")
            print("      - '朝阳万达广场'")
            print("      - '石景山万达广场'")
            print("      - '通州万达广场'")
        elif confusion_score >= 0.6:
            print("   ❗ 比较困惑的搜索结果")
            print("   📍 POI分布较散，建议细化搜索")
    
    print("\n" + "=" * 60 + "\n")

def demo_medium_confusion():
    """演示中等困惑度搜索"""
    print("⚡ 中等困惑示例 - 星巴克")
    print("=" * 40)
    
    keyword = "星巴克"
    city = "北京市"
    
    print(f"🔍 搜索: {keyword}")
    print(f"📍 城市: {city}")
    print()
    
    result = mcp_search_poi(keyword, city)
    
    if result.get("status"):
        data = result["data"]
        seg_info = data.get("segmentation_info", {})
        
        confusion_score = seg_info.get("confusion_score", 0)
        confusion_level = seg_info.get("confusion_level", "未知")
        
        print(f"📊 搜索结果:")
        print(f"   分词: {seg_info.get('segments', [])}")
        print(f"   POI数量: {data['count']} 个")
        print(f"   困惑度: {confusion_score:.3f} ({confusion_level})")
        print()
        
        print("☕ 前3个POI:")
        for i, poi in enumerate(data["pois"][:3], 1):
            print(f"   {i}. {poi['name']}")
            print(f"      地址: {poi['address']}")
        
        print()
        print("💡 分析:")
        if 0.4 <= confusion_score <= 0.6:
            print("   ⚠️ 中等困惑度")
            print("   📍 星巴克分布在不同商圈")
            print("   💭 用户可能需要考虑:")
            print("      - 距离当前位置的远近")
            print("      - 具体的商圈偏好")
            print("      - 营业时间等因素")
    
    print("\n" + "=" * 60 + "\n")

def demo_practical_applications():
    """演示实际应用场景"""
    print("🎯 实际应用场景")
    print("=" * 40)
    
    applications = [
        {
            "scenario": "智能推荐系统",
            "description": "根据困惑度调整推荐策略",
            "low_confusion": "直接推荐最近的POI",
            "high_confusion": "提供区域选择或细化搜索建议"
        },
        {
            "scenario": "语音助手",
            "description": "根据困惑度调整回复方式",
            "low_confusion": "'找到了海淀医院，为您导航'",
            "high_confusion": "'找到多个万达广场，您想去哪个区的？'"
        },
        {
            "scenario": "用户体验优化",
            "description": "根据困惑度提供不同的界面",
            "low_confusion": "直接显示地图和导航按钮",
            "high_confusion": "显示筛选选项和详细列表"
        }
    ]
    
    for app in applications:
        print(f"📱 {app['scenario']}:")
        print(f"   {app['description']}")
        print(f"   • 低困惑度: {app['low_confusion']}")
        print(f"   • 高困惑度: {app['high_confusion']}")
        print()

def demo_optimization_suggestions():
    """演示优化建议"""
    print("🚀 基于困惑度的优化建议")
    print("=" * 40)
    
    suggestions = [
        {
            "confusion_range": "0.0-0.2 (很低)",
            "user_experience": "优秀",
            "action": "直接提供导航或详细信息",
            "example": "海淀医院 → 直接导航到北京市海淀医院"
        },
        {
            "confusion_range": "0.2-0.4 (低)",
            "user_experience": "良好",
            "action": "提供简单的选择或确认",
            "example": "中关村软件园 → 显示2-3个最相关的选项"
        },
        {
            "confusion_range": "0.4-0.6 (中等)",
            "user_experience": "一般",
            "action": "提供区域筛选或更多信息",
            "example": "星巴克 → 按商圈分组显示"
        },
        {
            "confusion_range": "0.6-0.8 (高)",
            "user_experience": "较差",
            "action": "建议细化搜索条件",
            "example": "万达广场 → 建议添加区域名称"
        },
        {
            "confusion_range": "0.8-1.0 (很高)",
            "user_experience": "差",
            "action": "强烈建议重新搜索",
            "example": "银行 → 建议搜索'中国银行'或'附近的银行'"
        }
    ]
    
    for suggestion in suggestions:
        print(f"📊 {suggestion['confusion_range']}:")
        print(f"   用户体验: {suggestion['user_experience']}")
        print(f"   建议操作: {suggestion['action']}")
        print(f"   示例: {suggestion['example']}")
        print()

def main():
    """主演示函数"""
    print("🎉 POI搜索困惑度计算功能演示")
    print("🔧 通过距离分析评估搜索结果的明确性")
    print()
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请先设置环境变量 AMAP_API_KEY")
        print("   export AMAP_API_KEY='your_api_key_here'")
        return
    
    try:
        # 概念介绍
        demo_confusion_concept()
        
        # 清晰搜索示例
        demo_clear_search()
        
        # 困惑搜索示例
        demo_confusing_search()
        
        # 中等困惑示例
        demo_medium_confusion()
        
        # 实际应用场景
        demo_practical_applications()
        
        # 优化建议
        demo_optimization_suggestions()
        
        print("🎊 演示完成！")
        print()
        print("📝 困惑度功能总结:")
        print("   ✅ 自动计算POI搜索结果的困惑度")
        print("   ✅ 基于top3 POI之间的实际距离")
        print("   ✅ 提供0-1范围的标准化分数")
        print("   ✅ 300米以内为正常可接受范围")
        print("   ✅ 支持智能推荐和用户体验优化")
        print()
        print("🚀 现在所有POI搜索都会自动包含困惑度信息！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
