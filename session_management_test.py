#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理测试脚本
专门测试 session_id 功能和重新开始对话的能力
"""

import time
from taxi_agent_system import MainAgent


def test_session_creation():
    """测试会话创建功能"""
    print("🆕 会话创建测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 测试自动生成会话ID
    session1 = agent.start_new_session()
    print(f"自动生成会话ID: {session1}")
    
    # 测试指定会话ID
    session2 = agent.start_new_session("custom_session_123")
    print(f"指定会话ID: {session2}")
    
    # 测试会话信息
    info1 = agent.get_session_info(session1)
    info2 = agent.get_session_info(session2)
    
    print(f"会话1信息: {info1}")
    print(f"会话2信息: {info2}")
    
    print("✅ 会话创建测试完成\n")


def test_session_isolation():
    """测试会话隔离功能"""
    print("🔒 会话隔离测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 创建两个独立会话
    session_alice = agent.start_new_session("alice_session")
    session_bob = agent.start_new_session("bob_session")
    
    print(f"Alice会话ID: {session_alice}")
    print(f"Bob会话ID: {session_bob}")
    
    # Alice的对话
    print("\n--- Alice的对话 ---")
    alice_inputs = [
        "我想去西湖",
        "那里怎么样？",
        "从那里到杭州东站怎么走？"
    ]
    
    for i, user_input in enumerate(alice_inputs, 1):
        print(f"Alice第{i}轮: {user_input}")
        try:
            response = agent.process_user_input(user_input, session_alice)
            print(f"助手回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print()
    
    # Bob的对话
    print("--- Bob的对话 ---")
    bob_inputs = [
        "北京有哪些星巴克？",
        "推荐最近的一家",
        "那里怎么走？"  # 测试Bob的"那里"不会与Alice的混淆
    ]
    
    for i, user_input in enumerate(bob_inputs, 1):
        print(f"Bob第{i}轮: {user_input}")
        try:
            response = agent.process_user_input(user_input, session_bob)
            print(f"助手回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print()
    
    # 验证会话信息
    alice_info = agent.get_session_info(session_alice)
    bob_info = agent.get_session_info(session_bob)
    
    print("--- 会话信息验证 ---")
    print(f"Alice会话信息: {alice_info}")
    print(f"Bob会话信息: {bob_info}")
    
    print("✅ 会话隔离测试完成\n")


def test_session_restart():
    """测试会话重启功能"""
    print("🔄 会话重启测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 创建会话并进行对话
    session_id = agent.start_new_session("restart_test")
    print(f"初始会话ID: {session_id}")
    
    print("\n--- 第一轮对话 ---")
    first_inputs = [
        "我想去西湖",
        "那里有什么好玩的？"
    ]
    
    for i, user_input in enumerate(first_inputs, 1):
        print(f"第{i}轮: {user_input}")
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"助手回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print()
    
    # 检查会话信息
    info_before = agent.get_session_info(session_id)
    print(f"重启前会话信息: {info_before}")
    
    # 重启会话（使用相同的session_id）
    print("\n--- 重启会话 ---")
    new_session_id = agent.start_new_session(session_id)
    print(f"重启后会话ID: {new_session_id}")
    
    # 检查重启后的会话信息
    info_after = agent.get_session_info(new_session_id)
    print(f"重启后会话信息: {info_after}")
    
    print("\n--- 重启后对话 ---")
    second_inputs = [
        "那里有什么好玩的？",  # 这应该不会理解"那里"，因为上下文已清空
        "北京有哪些景点？"
    ]
    
    for i, user_input in enumerate(second_inputs, 1):
        print(f"第{i}轮: {user_input}")
        try:
            response = agent.process_user_input(user_input, new_session_id)
            print(f"助手回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print()
    
    print("✅ 会话重启测试完成\n")


def test_context_continuity():
    """测试上下文连续性"""
    print("🧠 上下文连续性测试")
    print("="*50)
    
    agent = MainAgent()
    session_id = agent.start_new_session("context_test")
    
    print(f"会话ID: {session_id}")
    
    # 测试上下文记忆的对话流程
    context_flow = [
        ("我想去西湖", "建立地点上下文"),
        ("那里怎么样？", "测试指代理解"),
        ("从那里到杭州东站怎么走？", "测试复合上下文"),
        ("打车要多少钱？", "测试路线记忆"),
        ("好的，帮我叫车", "测试完整信息记忆")
    ]
    
    for i, (user_input, description) in enumerate(context_flow, 1):
        print(f"\n第{i}轮 ({description}):")
        print(f"👤 用户: {user_input}")
        
        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"🤖 助手: {response}")
            
            # 显示会话信息
            session_info = agent.get_session_info(session_id)
            print(f"📊 会话状态: {session_info['message_count']}条消息")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)
    
    print("✅ 上下文连续性测试完成\n")


def test_concurrent_sessions():
    """测试并发会话处理"""
    print("⚡ 并发会话测试")
    print("="*50)
    
    agent = MainAgent()
    
    # 创建多个会话
    sessions = {}
    for i in range(3):
        session_id = agent.start_new_session(f"concurrent_session_{i+1}")
        sessions[f"user_{i+1}"] = session_id
        print(f"创建会话 user_{i+1}: {session_id}")
    
    # 模拟并发对话
    conversations = {
        "user_1": ["我在北京", "想去西湖", "怎么走？"],
        "user_2": ["找星巴克", "最近的在哪", "帮我叫车"],
        "user_3": ["我要打车", "从机场", "到市中心"]
    }
    
    # 交替处理不同用户的请求
    max_turns = max(len(conv) for conv in conversations.values())
    
    for turn in range(max_turns):
        print(f"\n--- 第{turn+1}轮并发交互 ---")
        
        for user, session_id in sessions.items():
            if turn < len(conversations[user]):
                user_input = conversations[user][turn]
                
                print(f"\n{user} ({session_id}):")
                print(f"👤 用户: {user_input}")
                
                try:
                    start_time = time.time()
                    response = agent.process_user_input(user_input, session_id)
                    response_time = time.time() - start_time
                    
                    print(f"🤖 助手: {response}")
                    print(f"⏱️  响应时间: {response_time:.2f}秒")
                    
                    # 显示会话状态
                    session_info = agent.get_session_info(session_id)
                    print(f"📊 会话状态: {session_info}")
                    
                except Exception as e:
                    print(f"❌ 错误: {e}")
    
    print("\n✅ 并发会话测试完成")
    print("💡 每个用户的会话都是独立的，不会相互干扰\n")


def main():
    """主测试函数"""
    print("🚀 会话管理功能测试开始")
    print("="*60)
    
    start_time = time.time()
    
    try:
        # 运行各项测试
        test_session_creation()
        test_session_isolation()
        test_session_restart()
        test_context_continuity()
        test_concurrent_sessions()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    
    total_time = time.time() - start_time
    
    print("="*60)
    print("🏁 会话管理测试完成")
    print(f"总耗时: {total_time:.2f}秒")
    print("="*60)
    print("\n📋 测试总结:")
    print("✅ 会话创建和管理")
    print("✅ 会话隔离和独立性")
    print("✅ 会话重启和上下文清理")
    print("✅ 上下文连续性")
    print("✅ 并发会话处理")
    print("\n🎯 核心功能验证:")
    print("- 支持自定义和自动生成会话ID")
    print("- 不同会话完全隔离，互不干扰")
    print("- 可以重新开始对话，清空上下文")
    print("- 单个会话内保持上下文连续性")
    print("- 支持多用户并发使用")


if __name__ == "__main__":
    main()
