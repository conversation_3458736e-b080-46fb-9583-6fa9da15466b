# 多轮对话测试脚本使用指南

本项目提供了多个测试脚本来验证 `taxi_agent_system` 的多轮对话能力。

## 📁 测试脚本文件

### 1. `demo_conversation_test.py` - 演示版测试脚本 ⭐ 推荐
**用途**: 无需API密钥的演示版本
**特点**:
- 使用模拟数据，无需真实API调用
- 完整展示多轮对话功能
- 包含4个核心演示场景
- 运行简单，适合快速体验

**运行方式**:
```bash
python demo_conversation_test.py
```

### 2. `quick_test.py` - 快速测试脚本
**用途**: 快速验证基本功能
**特点**: 
- 自动化测试，无需交互
- 覆盖基本对话、上下文记忆、参数确认、错误处理等
- 包含性能测试
- 运行时间短（约1-2分钟）

**运行方式**:
```bash
python quick_test.py
```

### 2. `interactive_chat_test.py` - 交互式测试脚本
**用途**: 手动交互测试
**特点**:
- 提供命令行聊天界面
- 支持实时对话测试
- 内置统计和历史记录功能
- 可运行预设测试场景

**运行方式**:
```bash
python interactive_chat_test.py
```

**可用命令**:
- `/help` - 显示帮助信息
- `/history` - 显示对话历史
- `/clear` - 清空对话历史
- `/stats` - 显示统计信息
- `/test` - 运行预设测试场景
- `/quit` - 退出程序

### 3. `multi_turn_conversation_test.py` - 完整测试套件
**用途**: 全面的自动化测试
**特点**:
- 8个完整的对话场景
- 压力测试功能
- 详细的测试报告
- 性能监控和统计

**运行方式**:
```bash
python multi_turn_conversation_test.py
```

### 4. `taxi_agent_system.py` - 主系统（已增强）
**新增功能**:
- 在原有系统基础上增加了多轮对话测试函数
- 保持向后兼容性

## 🧪 测试场景说明

### 基础功能测试
- **地点查询**: "西湖在哪里？"
- **POI搜索**: "北京有哪些星巴克？"
- **路线规划**: "从天安门到鸟巢怎么走？"
- **打车服务**: "我要从康德大厦打车到大悦城"

### 多轮对话测试
1. **完整打车流程**
   ```
   用户: 我想打车
   用户: 从康德大厦出发
   用户: 去大悦城
   用户: 要舒适型车辆
   用户: 确认订单
   ```

2. **上下文记忆测试**
   ```
   用户: 我想去西湖
   用户: 那里怎么样？        # 测试指代理解
   用户: 从那里到杭州东站怎么走？  # 测试上下文记忆
   用户: 打车要多少钱？      # 测试路线记忆
   ```

3. **参数确认流程**
   ```
   用户: 帮我叫车
   用户: 从北京大学
   用户: 到清华大学
   用户: 要豪华型车辆
   用户: 确认起点
   ```

### 错误处理测试
- **无效输入**: "我要去火星"
- **模糊地点**: "我要去大厦"
- **缺失参数**: "我要打车"（不提供起点终点）
- **错误恢复**: 从错误状态恢复到正常对话

### 会话管理测试
- 多个并发会话的独立性
- 会话间上下文隔离
- 会话状态管理

## 📊 测试报告示例

运行测试后会生成详细报告，包括：

```
测试报告
============================================================
总场景数: 8
成功场景: 7
成功率: 87.5%
总对话轮数: 35
总错误数: 3
错误率: 8.6%

响应时间统计:
平均响应时间: 1.23秒
最快响应: 0.45秒
最慢响应: 3.21秒

详细场景结果:
- 完整打车流程: ✅ 成功 (耗时: 5.2s, 错误: 0)
- 地点查询和导航: ✅ 成功 (耗时: 4.8s, 错误: 0)
- POI搜索和推荐: ✅ 成功 (耗时: 6.1s, 错误: 1)
...
```

## 🚀 快速开始

1. **环境准备**
   ```bash
   # 确保已安装必要的依赖
   pip install openai
   
   # 设置环境变量
   export BAILIAN_API_KEY="your_api_key"
   export AMAP_API_KEY="your_amap_key"
   ```

2. **运行快速测试**
   ```bash
   python quick_test.py
   ```

3. **交互式测试**
   ```bash
   python interactive_chat_test.py
   ```

4. **完整测试套件**
   ```bash
   python multi_turn_conversation_test.py
   ```

## 🔧 自定义测试

### 添加新的测试场景
在 `multi_turn_conversation_test.py` 中的 `get_test_scenarios()` 函数中添加新场景：

```python
{
    "name": "自定义场景名称",
    "description": "场景描述",
    "turns": [
        "第一轮用户输入",
        "第二轮用户输入",
        "第三轮用户输入"
    ]
}
```

### 修改测试参数
- 调整响应时间阈值
- 修改错误容忍度
- 自定义统计指标

## 📝 注意事项

1. **API配置**: 确保正确设置了 `BAILIAN_API_KEY` 和 `AMAP_API_KEY`
2. **网络连接**: 测试需要网络连接来调用API
3. **响应时间**: 实际响应时间取决于网络状况和API服务性能
4. **错误处理**: 测试脚本会捕获并记录所有错误，不会中断测试流程
5. **会话隔离**: 每个测试场景使用独立的会话ID，确保测试结果不相互影响

## 🐛 故障排除

### 常见问题
1. **API密钥错误**: 检查环境变量设置
2. **网络超时**: 检查网络连接，可能需要重试
3. **导入错误**: 确保 `taxi_agent_system.py` 在同一目录
4. **权限错误**: 确保有文件读写权限

### 调试建议
- 使用 `interactive_chat_test.py` 进行单步调试
- 查看详细的错误日志
- 检查 `taxi_agent_system.py` 中的调试输出

## 📈 性能优化建议

1. **批量测试**: 使用 `multi_turn_conversation_test.py` 进行批量测试
2. **并发控制**: 避免过多并发请求导致API限流
3. **缓存机制**: 考虑为重复查询添加缓存
4. **超时设置**: 合理设置API调用超时时间

---

如有问题或建议，请查看代码注释或联系开发团队。
