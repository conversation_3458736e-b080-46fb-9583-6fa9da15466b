"""
智能打车助手 - 交互式演示应用
基于 Streamlit 构建的现代化 AI 聊天界面
"""

import streamlit as st
import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

# 导入我们的打车系统
try:
    from taxi_agent_system import EnhancedTaxiAgent
    TAXI_AGENT_AVAILABLE = True
except ImportError as e:
    st.error(f"无法导入打车系统: {e}")
    TAXI_AGENT_AVAILABLE = False

# 页面配置
st.set_page_config(
    page_title="智能生活助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .chat-container {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .user-message {
        background-color: #007bff;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        margin: 0.5rem 0;
        text-align: right;
    }
    
    .assistant-message {
        background-color: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        margin: 0.5rem 0;
    }
    
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
def init_session_state():
    """初始化会话状态"""
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'taxi_agent' not in st.session_state and TAXI_AGENT_AVAILABLE:
        st.session_state.taxi_agent = EnhancedTaxiAgent()
    
    if 'conversation_stats' not in st.session_state:
        st.session_state.conversation_stats = {
            'total_messages': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'start_time': datetime.now()
        }

def display_header():
    """显示页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🚗 智能打车助手</h1>
        <p>基于高德地图API和AI技术的智能打车服务，支持地点查询、POI搜索和打车服务</p>
    </div>
    """, unsafe_allow_html=True)

def display_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.header("🛠️ 系统状态")
        
        # 系统状态指示器
        if TAXI_AGENT_AVAILABLE:
            st.success("✅ 打车系统已就绪")
        else:
            st.error("❌ 打车系统不可用")
        
        # 会话信息
        st.subheader("📊 会话统计")
        stats = st.session_state.conversation_stats
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("总消息数", stats['total_messages'])
            st.metric("成功请求", stats['successful_requests'])
        with col2:
            st.metric("失败请求", stats['failed_requests'])
            if stats['total_messages'] > 0:
                success_rate = (stats['successful_requests'] / stats['total_messages']) * 100
                st.metric("成功率", f"{success_rate:.1f}%")
        
        # 功能说明
        st.subheader("🎯 支持功能")
        st.markdown("""
        - 🗺️ **地点查询**: 获取地点的经纬度坐标
        - 🏙️ **城市信息**: 查询城市代码和信息
        - 📍 **POI搜索**: 搜索兴趣点和商户
        - 🚗 **打车服务**: 智能打车和价格估算
        - 💬 **智能对话**: 简洁明了的语音友好回复
        """)
        
        # 示例查询
        st.subheader("💡 示例查询")
        example_queries = [
            "西湖在哪里？",
            "帮我查一下杭州的城市代码",
            "北京有哪些星巴克？",
            "我要从康德大厦打车到太阳宫",
            "从北京站到首都机场，要舒适型车辆",
            "今天天气怎么样？"  # 超出范围的示例
        ]
        
        for query in example_queries:
            if st.button(query, key=f"example_{hash(query)}"):
                st.session_state.example_query = query
        
        # 重置会话按钮
        if st.button("🔄 重置会话"):
            # 生成新的session ID
            old_session_id = st.session_state.session_id
            st.session_state.session_id = str(uuid.uuid4())

            # 清除对话历史
            st.session_state.messages = []
            st.session_state.conversation_stats = {
                'total_messages': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'start_time': datetime.now()
            }

            # 清除Agent中的会话上下文
            if TAXI_AGENT_AVAILABLE and 'taxi_agent' in st.session_state:
                agent = st.session_state.taxi_agent
                if hasattr(agent, 'context') and old_session_id in agent.context:
                    del agent.context[old_session_id]
                # 清除状态管理器中的会话相关状态
                if hasattr(agent, 'state'):
                    agent.state.conversation_state.pop(old_session_id, None)

            st.success(f"会话已重置！新会话ID: {st.session_state.session_id[:8]}...")
            st.rerun()

def display_chat_interface():
    """显示聊天界面"""
    st.subheader("💬 对话界面")
    
    # 显示聊天历史
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
                
                # 如果有额外的数据，显示结构化信息
                if "data" in message:
                    with st.expander("📊 详细信息"):
                        st.json(message["data"])

def process_user_input(user_input: str) -> tuple[str, dict]:
    """处理用户输入并返回响应"""
    if not TAXI_AGENT_AVAILABLE:
        return "抱歉，打车系统当前不可用。请检查系统配置。", {}

    try:
        # 调用打车Agent处理用户输入
        response = st.session_state.taxi_agent.process_message(
            user_input,
            st.session_state.session_id
        )

        # 尝试获取系统状态信息
        agent_state = st.session_state.taxi_agent.state
        additional_data = {
            "timestamp": datetime.now().isoformat(),
            "session_id": st.session_state.session_id,
            "status": agent_state.current_status if hasattr(agent_state, 'current_status') else None
        }

        # 更新统计信息
        st.session_state.conversation_stats['successful_requests'] += 1

        return response, additional_data

    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        st.session_state.conversation_stats['failed_requests'] += 1
        return error_msg, {"error": str(e)}

def display_system_metrics():
    """显示系统性能指标"""
    if not TAXI_AGENT_AVAILABLE:
        return

    st.subheader("📈 系统性能")

    try:
        assistant = st.session_state.life_assistant
        debug_agent = assistant.debug_agent
        
        # 创建性能指标图表
        col1, col2 = st.columns(2)
        
        with col1:
            # 请求成功率饼图
            stats = st.session_state.conversation_stats
            if stats['total_messages'] > 0:
                success_data = pd.DataFrame({
                    'Status': ['成功', '失败'],
                    'Count': [stats['successful_requests'], stats['failed_requests']]
                })
                
                fig_pie = px.pie(success_data, values='Count', names='Status', 
                               title="请求成功率分布")
                st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # 响应时间趋势（模拟数据）
            if hasattr(debug_agent, 'performance_metrics'):
                metrics = debug_agent.performance_metrics
                if metrics.get('response_times'):
                    time_data = pd.DataFrame({
                        'Request': range(1, len(metrics['response_times']) + 1),
                        'Response Time (ms)': metrics['response_times']
                    })
                    
                    fig_line = px.line(time_data, x='Request', y='Response Time (ms)',
                                     title="响应时间趋势")
                    st.plotly_chart(fig_line, use_container_width=True)
    
    except Exception as e:
        st.warning(f"无法显示系统指标: {e}")

def main():
    """主函数"""
    # 初始化
    init_session_state()
    
    # 显示界面
    display_header()
    display_sidebar()
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        display_chat_interface()
        
        # 处理示例查询
        if hasattr(st.session_state, 'example_query'):
            user_input = st.session_state.example_query
            del st.session_state.example_query
        else:
            # 聊天输入框
            user_input = st.chat_input("请输入您的问题...")
        
        # 处理用户输入
        if user_input:
            # 添加用户消息到历史
            st.session_state.messages.append({
                "role": "user", 
                "content": user_input
            })
            
            # 更新统计
            st.session_state.conversation_stats['total_messages'] += 1
            
            # 显示用户消息
            with st.chat_message("user"):
                st.markdown(user_input)
            
            # 处理并显示助手回复
            with st.chat_message("assistant"):
                with st.spinner("正在处理您的请求..."):
                    response, data = process_user_input(user_input)
                
                st.markdown(response)
                
                # 添加助手回复到历史
                message_data = {"role": "assistant", "content": response}
                if data:
                    message_data["data"] = data
                
                st.session_state.messages.append(message_data)
            
            # 刷新页面以显示新消息
            st.rerun()
    
    with col2:
        display_system_metrics()

if __name__ == "__main__":
    main()
