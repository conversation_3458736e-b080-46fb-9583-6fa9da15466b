#!/usr/bin/env python3
"""
测试具体的None参数案例：
"我要打车从方正大厦走" - 这个例子中end_place应该为None
验证validation_result返回{'can_execute': False}
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_test_header(title):
    """打印测试标题"""
    print("\n" + "🎯" + "="*78)
    print(f"  {title}")
    print("🎯" + "="*78)

def test_specific_none_case():
    """测试具体的None参数案例"""
    print_test_header("具体None参数案例测试")
    
    print("📋 测试目标:")
    print("   测试用例: '我要打车从方正大厦走'")
    print("   预期: end_place为None，validation_result返回{'can_execute': False}")
    
    agent = MainAgent()
    session_id = f"specific_none_test_{datetime.now().strftime('%H%M%S')}"
    
    user_input = "我要打车从方正大厦走"
    
    print(f"\n👤 用户输入: {user_input}")
    print(f"🎯 预期结果: end_place为None，不能执行")
    
    print(f"\n{'='*60}")
    print(f"开始处理...")
    print(f"{'='*60}")
    
    try:
        response = agent.process_user_input(user_input, session_id)
        
        print(f"\n🤖 系统回复: {response}")
        
        # 分析action状态历史
        if hasattr(agent.enhanced_agent, 'action_state_hist'):
            hist = agent.enhanced_agent.action_state_hist
            if hist:
                print(f"\n📊 Action状态历史分析:")
                for i, action in enumerate(hist, 1):
                    can_execute = action.get("can_execute", False)
                    action_type = action.get("action_type", "")
                    error_msg = action.get("error", "")
                    
                    print(f"   动作{i}: can_execute={can_execute}")
                    if not can_execute:
                        print(f"   ✅ 正确 - 不能执行")
                        if action_type:
                            print(f"   操作类型: {action_type}")
                        if error_msg:
                            print(f"   错误信息: {error_msg}")
                        
                        # 检查是否是因为缺少end_place
                        missing_params = action.get("missing_parameters", [])
                        if "end_place" in missing_params:
                            print(f"   ✅ 正确识别 - end_place缺失")
                        
                        validation_errors = action.get("validation_errors", [])
                        if validation_errors:
                            print(f"   验证错误: {validation_errors}")
                    else:
                        print(f"   ❌ 错误 - 不应该能执行")
        
        # 检查是否正确识别了缺少目的地
        expected_keywords = ["去哪", "目的地", "哪里", "终点"]
        response_lower = response.lower()
        matched_keywords = [kw for kw in expected_keywords if kw in response_lower]
        
        print(f"\n📊 回复分析:")
        print(f"   期望关键词: {expected_keywords}")
        print(f"   匹配关键词: {matched_keywords}")
        
        if matched_keywords:
            print(f"   ✅ 正确 - 系统识别到缺少目的地信息")
        else:
            print(f"   ⚠️  注意 - 系统可能没有明确询问目的地")
        
    except Exception as e:
        print(f"❌ 处理出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_similar_cases():
    """测试类似的不完整参数案例"""
    print_test_header("类似不完整参数案例测试")
    
    print("📋 测试目标:")
    print("   测试各种不完整的打车请求")
    print("   验证系统正确识别缺失参数")
    
    agent = MainAgent()
    session_id = f"similar_test_{datetime.now().strftime('%H%M%S')}"
    
    test_cases = [
        {
            "input": "我要打车从方正大厦走",
            "missing_param": "end_place",
            "description": "缺少目的地"
        },
        {
            "input": "我要打车去大兴机场",
            "missing_param": "start_place",
            "description": "缺少起点（如果系统没有自动推断）"
        },
        {
            "input": "帮我叫个车从公司出发",
            "missing_param": "end_place",
            "description": "起点模糊，缺少目的地"
        },
        {
            "input": "我想去机场",
            "missing_param": "start_place",
            "description": "缺少起点，目的地也模糊"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🧪 测试案例 {i}")
        print(f"👤 用户输入: {case['input']}")
        print(f"📝 描述: {case['description']}")
        print(f"🎯 预期缺失: {case['missing_param']}")
        
        try:
            response = agent.process_user_input(case['input'], session_id)
            print(f"🤖 系统回复: {response}")
            
            # 检查最新的action状态
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    latest_action = hist[-1]
                    can_execute = latest_action.get("can_execute", False)
                    
                    if not can_execute:
                        print(f"   ✅ 正确 - 不能执行")
                        
                        missing_params = latest_action.get("missing_parameters", [])
                        if missing_params:
                            print(f"   缺少参数: {missing_params}")
                            if case['missing_param'] in missing_params:
                                print(f"   ✅ 正确识别缺失参数: {case['missing_param']}")
                        
                        action_type = latest_action.get("action_type", "")
                        if action_type == "provide_parameters":
                            print(f"   ✅ 正确操作类型: 要求提供参数")
                    else:
                        print(f"   ❌ 错误 - 不应该能执行")
            
        except Exception as e:
            print(f"   ❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*60}")

def test_parameter_completion_flow():
    """测试参数补全流程"""
    print_test_header("参数补全流程测试")
    
    print("📋 测试目标:")
    print("   测试从不完整到完整的参数补全流程")
    
    agent = MainAgent()
    session_id = f"completion_test_{datetime.now().strftime('%H%M%S')}"
    
    # 模拟完整的参数补全对话
    conversation_steps = [
        {
            "input": "我要打车从方正大厦走",
            "expected": "系统询问目的地",
            "check": "end_place缺失"
        },
        {
            "input": "去大兴机场",
            "expected": "系统验证参数并请求确认",
            "check": "参数补全完成"
        }
    ]
    
    for i, step in enumerate(conversation_steps, 1):
        print(f"\n📝 对话步骤 {i}")
        print(f"👤 用户: {step['input']}")
        print(f"🎯 预期: {step['expected']}")
        print(f"🔍 检查: {step['check']}")
        
        try:
            response = agent.process_user_input(step['input'], session_id)
            print(f"🤖 助手: {response}")
            
            # 分析当前状态
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    latest_action = hist[-1]
                    can_execute = latest_action.get("can_execute", False)
                    action_type = latest_action.get("action_type", "")
                    
                    print(f"📊 当前状态:")
                    print(f"   can_execute: {can_execute}")
                    print(f"   action_type: {action_type}")
                    
                    if i == 1:  # 第一步应该不能执行
                        if not can_execute and action_type == "provide_parameters":
                            print(f"   ✅ 第{i}步正确 - 识别参数缺失")
                        else:
                            print(f"   ❌ 第{i}步错误 - 应该识别参数缺失")
                    elif i == 2:  # 第二步可能需要确认
                        if not can_execute and action_type in ["confirm_with_candidates", "fix_parameters"]:
                            print(f"   ✅ 第{i}步正确 - 需要验证或确认")
                        elif can_execute:
                            print(f"   ✅ 第{i}步正确 - 可以执行")
                        else:
                            print(f"   ⚠️  第{i}步状态: {action_type}")
            
        except Exception as e:
            print(f"❌ 步骤{i}出错: {str(e)}")
        
        print(f"\n{'.'*50}")

def main():
    """主函数"""
    print("🔧 具体None参数案例测试")
    print("测试: '我要打车从方正大厦走' - end_place为None的情况")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. 具体None参数案例测试")
    print("2. 类似不完整参数案例测试")
    print("3. 参数补全流程测试")
    print("4. 全部测试")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            test_specific_none_case()
            break
        elif choice == "2":
            test_similar_cases()
            break
        elif choice == "3":
            test_parameter_completion_flow()
            break
        elif choice == "4":
            test_specific_none_case()
            test_similar_cases()
            test_parameter_completion_flow()
            break
        elif choice == "5":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-5")

if __name__ == "__main__":
    main()
