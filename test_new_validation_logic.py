#!/usr/bin/env python3
"""
测试新的函数验证逻辑
验证两类函数的不同处理策略：
1. call_taxi_service: 需要POI校验，困惑度检查，候选信息提示
2. 其他函数: 只需完备性校验
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_test_header(title):
    """打印测试标题"""
    print("\n" + "🎯" + "="*78)
    print(f"  {title}")
    print("🎯" + "="*78)

def test_taxi_service_validation():
    """测试打车服务的POI验证逻辑"""
    print_test_header("打车服务POI验证测试")
    
    print("📋 测试目标:")
    print("   1. 验证call_taxi_service需要POI校验")
    print("   2. 测试困惑度检查机制（≤0.2通过，>0.2失败）")
    print("   3. 验证候选信息加入prompt的逻辑")
    print("   4. 测试用户确认流程")
    
    agent = MainAgent()
    session_id = f"taxi_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试不同困惑度的地理位置
    test_cases = [
        {
            "input": "我要从方正大厦打车去大兴机场",
            "description": "明确的地理位置，困惑度应该较低",
            "expected": "POI验证通过，显示候选信息，请求用户确认"
        },
        {
            "input": "我要从机场打车去市中心",
            "description": "模糊的地理位置，困惑度应该较高",
            "expected": "POI验证失败，要求用户提供更具体信息"
        },
        {
            "input": "从北京大学打车去清华大学",
            "description": "知名地点，困惑度应该很低",
            "expected": "POI验证通过，直接显示确认方案"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}")
        print(f"👤 用户输入: {test_case['input']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"🎯 预期结果: {test_case['expected']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test_case['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'.'*80}")
        if i < len(test_cases):
            input("按回车继续下一个测试用例...")

def test_other_functions_validation():
    """测试其他函数的完备性验证"""
    print_test_header("其他函数完备性验证测试")
    
    print("📋 测试目标:")
    print("   1. 验证其他函数只需完备性校验")
    print("   2. 测试必填参数检查")
    print("   3. 验证参数完整后直接执行")
    
    agent = MainAgent()
    session_id = f"other_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试不同类型的函数
    test_cases = [
        {
            "input": "搜索附近的星巴克",
            "function": "mcp_search_poi",
            "description": "POI搜索，高容错率函数",
            "expected": "只检查完备性，直接执行"
        },
        {
            "input": "查询杭州西湖的坐标",
            "function": "mcp_geocode_address", 
            "description": "地址转坐标，高容错率函数",
            "expected": "只检查完备性，直接执行"
        },
        {
            "input": "计算从天安门到故宫的距离",
            "function": "mcp_calculate_poi_to_poi_route",
            "description": "路线计算，高容错率函数", 
            "expected": "只检查完备性，直接执行"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}")
        print(f"👤 用户输入: {test_case['input']}")
        print(f"🔧 预期函数: {test_case['function']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"🎯 预期结果: {test_case['expected']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test_case['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*80}")
        if i < len(test_cases):
            input("按回车继续下一个测试用例...")

def test_confusion_score_handling():
    """测试困惑度处理逻辑"""
    print_test_header("困惑度处理逻辑测试")
    
    print("📋 测试目标:")
    print("   1. 验证困惑度≤0.2的处理（获取候选信息）")
    print("   2. 验证困惑度>0.2的处理（要求澄清）")
    print("   3. 测试候选信息在prompt中的展示")
    
    agent = MainAgent()
    session_id = f"confusion_test_{datetime.now().strftime('%H%M%S')}"
    
    # 专门测试困惑度边界情况
    test_cases = [
        {
            "input": "我要打车去机场",
            "description": "模糊地点'机场'，困惑度应该>0.2",
            "expected": "验证失败，要求用户澄清具体机场"
        },
        {
            "input": "我要打车去北京大兴国际机场",
            "description": "具体地点，困惑度应该≤0.2",
            "expected": "验证通过，显示候选信息和确认方案"
        },
        {
            "input": "从火车站打车去酒店",
            "description": "两个都是模糊地点，困惑度都应该>0.2",
            "expected": "验证失败，要求用户提供具体地点"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 困惑度测试 {i}")
        print(f"👤 用户输入: {test_case['input']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"🎯 预期结果: {test_case['expected']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test_case['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*80}")
        if i < len(test_cases):
            input("按回车继续下一个测试...")

def test_city_environment():
    """测试城市环境变量的使用"""
    print_test_header("城市环境变量测试")
    
    print("📋 测试目标:")
    print("   1. 验证DEFAULT_CITY环境变量的使用")
    print("   2. 测试城市信息在prompt中的体现")
    print("   3. 验证城市上下文对地理位置解析的影响")
    
    # 显示当前城市设置
    from taxi_agent_system import DEFAULT_CITY
    print(f"\n🏙️  当前默认城市: {DEFAULT_CITY}")
    
    agent = MainAgent()
    session_id = f"city_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试城市相关的地理位置
    test_cases = [
        {
            "input": "我要打车去天安门",
            "description": f"在{DEFAULT_CITY}环境下的知名地点",
            "expected": f"应该解析为{DEFAULT_CITY}的天安门"
        },
        {
            "input": "从西湖打车去火车站",
            "description": "可能存在多个城市的地点",
            "expected": f"应该优先考虑{DEFAULT_CITY}的相关地点"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 城市测试 {i}")
        print(f"👤 用户输入: {test_case['input']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"🎯 预期结果: {test_case['expected']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test_case['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*80}")
        if i < len(test_cases):
            input("按回车继续下一个测试...")

def main():
    """主函数"""
    print("🔧 新验证逻辑测试")
    print("测试基于函数类型的不同验证策略")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    # 显示环境配置
    from taxi_agent_system import DEFAULT_CITY, DEFAULT_LONGITUDE, DEFAULT_LATITUDE
    print(f"\n🌍 环境配置:")
    print(f"   默认城市: {DEFAULT_CITY}")
    print(f"   默认坐标: {DEFAULT_LONGITUDE}, {DEFAULT_LATITUDE}")
    
    print("\n选择测试模式:")
    print("1. 打车服务POI验证测试")
    print("2. 其他函数完备性验证测试")
    print("3. 困惑度处理逻辑测试")
    print("4. 城市环境变量测试")
    print("5. 全部测试")
    print("6. 退出")
    
    while True:
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == "1":
            test_taxi_service_validation()
            break
        elif choice == "2":
            test_other_functions_validation()
            break
        elif choice == "3":
            test_confusion_score_handling()
            break
        elif choice == "4":
            test_city_environment()
            break
        elif choice == "5":
            test_taxi_service_validation()
            test_other_functions_validation()
            test_confusion_score_handling()
            test_city_environment()
            break
        elif choice == "6":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-6")

if __name__ == "__main__":
    main()
