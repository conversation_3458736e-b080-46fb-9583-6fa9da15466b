#!/usr/bin/env python3
"""
测试详细的function calling信息打印
展示：
1. function calling的入参详情
2. 参数状态分析（待填写、待确认等）
3. 具体的执行方案和可选项
4. function response的详细信息
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_test_header(title):
    """打印测试标题"""
    print("\n" + "🎯" + "="*78)
    print(f"  {title}")
    print("🎯" + "="*78)

def test_detailed_function_calling():
    """测试详细的function calling信息"""
    print_test_header("详细Function Calling信息测试")
    
    print("📋 测试目标:")
    print("   1. 打印function calling的入参详情")
    print("   2. 分析参数状态（待填写、待确认等）")
    print("   3. 显示具体执行方案和可选项")
    print("   4. 展示function response详细信息")
    
    # 初始化Agent
    print("\n🚀 初始化MainAgent...")
    agent = MainAgent()
    session_id = f"detailed_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试对话序列 - 专门设计来触发不同的参数状态
    test_conversations = [
        {
            "input": "我想打车去大兴机场",
            "description": "触发call_taxi_service，应该显示缺少起点参数",
            "expected_states": ["缺少必填参数", "待填写状态"]
        },
        {
            "input": "从方正大厦出发",
            "description": "补充起点信息，应该显示参数验证和待确认状态",
            "expected_states": ["参数验证", "待确认状态", "具体方案"]
        },
        {
            "input": "确认打车",
            "description": "用户确认，应该执行打车服务",
            "expected_states": ["参数确认", "执行成功"]
        }
    ]
    
    for i, conv in enumerate(test_conversations, 1):
        print_test_header(f"测试轮次 {i}")
        print(f"👤 用户输入: {conv['input']}")
        print(f"📝 测试描述: {conv['description']}")
        print(f"🎯 预期状态: {conv['expected_states']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理用户输入...")
        print(f"{'='*60}")
        
        try:
            # 处理用户输入 - 这里会触发详细的function calling打印
            response = agent.process_user_input(conv['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统最终回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'.'*80}")
        if i < len(test_conversations):
            input("按回车继续下一轮测试...")

def test_different_function_types():
    """测试不同类型函数的详细信息"""
    print_test_header("不同函数类型的详细信息测试")
    
    agent = MainAgent()
    session_id = f"types_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试不同类型的函数调用
    function_tests = [
        {
            "input": "搜索附近的星巴克",
            "description": "高容错率函数 - mcp_search_poi",
            "expected": "应该直接执行，无需确认"
        },
        {
            "input": "计算从方正大厦到大兴机场的距离",
            "description": "高容错率函数 - mcp_calculate_poi_to_poi_route", 
            "expected": "应该直接执行，显示路线信息"
        },
        {
            "input": "估算从方正大厦到大兴机场的打车费用",
            "description": "低容错率函数 - mcp_estimate_taxi_price",
            "expected": "需要用户确认参数"
        }
    ]
    
    for i, test in enumerate(function_tests, 1):
        print(f"\n🧪 函数测试 {i}")
        print(f"👤 用户输入: {test['input']}")
        print(f"📝 测试描述: {test['description']}")
        print(f"🎯 预期行为: {test['expected']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*80}")
        if i < len(function_tests):
            input("按回车继续下一个函数测试...")

def test_parameter_states_progression():
    """测试参数状态的完整变化过程"""
    print_test_header("参数状态变化过程测试")
    
    print("📋 测试目标: 观察参数从'未填写'到'已确认'的完整变化")
    
    agent = MainAgent()
    session_id = f"states_test_{datetime.now().strftime('%H%M%S')}"
    
    # 设计一个完整的参数状态变化序列
    state_progression = [
        {
            "input": "打车",
            "expected_state": "未填写 - 缺少起点和终点",
            "description": "初始状态，所有参数未填写"
        },
        {
            "input": "去机场",
            "expected_state": "未校验 - 终点模糊，起点缺失",
            "description": "部分参数提供，但需要澄清"
        },
        {
            "input": "从方正大厦去大兴机场",
            "expected_state": "已校验待确认 - 参数验证通过",
            "description": "参数完整且验证通过，等待用户确认"
        },
        {
            "input": "确认",
            "expected_state": "已确认 - 可以执行",
            "description": "用户确认，参数状态完整"
        }
    ]
    
    for i, step in enumerate(state_progression, 1):
        print(f"\n📊 状态变化步骤 {i}")
        print(f"👤 用户输入: {step['input']}")
        print(f"🎯 预期状态: {step['expected_state']}")
        print(f"📝 描述: {step['description']}")
        
        print(f"\n{'='*60}")
        print(f"处理中...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(step['input'], session_id)
            
            print(f"\n{'='*60}")
            print(f"🤖 系统回复:")
            print(f"{'='*60}")
            print(f"{response}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print(f"\n{'.'*80}")
        if i < len(state_progression):
            input("按回车继续下一个状态变化...")

def main():
    """主函数"""
    print("🔧 详细Function Calling信息测试")
    print("展示function calling的完整信息，包括参数状态、执行方案等")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. 详细Function Calling信息测试 (推荐)")
    print("2. 不同函数类型测试")
    print("3. 参数状态变化过程测试")
    print("4. 全部测试")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            test_detailed_function_calling()
            break
        elif choice == "2":
            test_different_function_types()
            break
        elif choice == "3":
            test_parameter_states_progression()
            break
        elif choice == "4":
            test_detailed_function_calling()
            test_different_function_types()
            test_parameter_states_progression()
            break
        elif choice == "5":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-5")

if __name__ == "__main__":
    main()
