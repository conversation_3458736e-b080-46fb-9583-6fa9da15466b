"""
Flask API 服务器 - 为前端提供打车助手API接口
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any
import logging

# 导入我们的打车系统
try:
    from taxi_agent_system import EnhancedTaxiAgent
    TAXI_AGENT_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入打车系统: {e}")
    TAXI_AGENT_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量存储agent实例和会话
taxi_agent = None
sessions = {}  # 存储不同会话的上下文

def init_taxi_agent():
    """初始化打车Agent"""
    global taxi_agent
    if TAXI_AGENT_AVAILABLE and taxi_agent is None:
        try:
            taxi_agent = EnhancedTaxiAgent()
            logger.info("打车Agent初始化成功")
            return True
        except Exception as e:
            logger.error(f"打车Agent初始化失败: {e}")
            return False
    return taxi_agent is not None

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'taxi_agent_available': TAXI_AGENT_AVAILABLE and taxi_agent is not None
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天接口"""
    try:
        # 检查请求数据
        if not request.is_json:
            return jsonify({'error': '请求必须是JSON格式'}), 400
        
        data = request.get_json()
        message = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 初始化打车Agent（如果需要）
        if not init_taxi_agent():
            return jsonify({
                'error': '打车系统暂不可用',
                'response': '抱歉，打车系统当前不可用。请稍后再试。',
                'additional_data': {
                    'error_type': 'system_unavailable',
                    'timestamp': datetime.now().isoformat()
                }
            }), 503
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 处理消息
        logger.info(f"处理消息 [会话: {session_id}]: {message}")
        
        try:
            response = taxi_agent.process_message(message, session_id)
            
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 获取系统状态信息
            additional_data = {
                'session_id': session_id,
                'response_time_ms': round(response_time, 2),
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            }
            
            # 尝试获取打车Agent的状态信息
            try:
                if hasattr(taxi_agent, 'state') and hasattr(taxi_agent.state, 'current_status'):
                    additional_data['agent_status'] = taxi_agent.state.current_status

                # 获取会话信息
                if hasattr(taxi_agent, 'context') and session_id in taxi_agent.context:
                    context = taxi_agent.context[session_id]
                    additional_data['message_count'] = len([msg for msg in context if msg.get("role") in ["user", "assistant"]])

            except Exception as e:
                logger.warning(f"获取打车Agent状态信息失败: {e}")
            
            logger.info(f"响应生成成功 [会话: {session_id}] 耗时: {response_time:.2f}ms")
            
            return jsonify({
                'response': response,
                'additional_data': additional_data
            })
            
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            logger.error(f"处理消息失败 [会话: {session_id}]: {e}")
            
            return jsonify({
                'error': error_msg,
                'response': '抱歉，处理您的请求时出现了问题。请稍后再试。',
                'additional_data': {
                    'session_id': session_id,
                    'error_type': 'processing_error',
                    'error_details': str(e),
                    'timestamp': datetime.now().isoformat(),
                    'status': 'error'
                }
            }), 500
            
    except Exception as e:
        logger.error(f"API请求处理失败: {e}")
        return jsonify({
            'error': '服务器内部错误',
            'response': '抱歉，服务器出现了问题。请稍后再试。'
        }), 500

@app.route('/api/sessions/<session_id>/history', methods=['GET'])
def get_session_history(session_id):
    """获取会话历史"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503

        # 尝试从打车Agent获取会话历史
        history = []
        if hasattr(taxi_agent, 'context') and session_id in taxi_agent.context:
            context = taxi_agent.context[session_id]
            # 过滤掉系统消息，只返回用户和助手的对话
            history = [msg for msg in context if msg.get('role') in ['user', 'assistant']]
        
        return jsonify({
            'session_id': session_id,
            'history': history,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取会话历史失败: {e}")
        return jsonify({'error': '获取会话历史失败'}), 500

@app.route('/api/sessions/new', methods=['POST'])
def create_new_session():
    """创建新会话"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503

        data = request.get_json() or {}
        session_id = data.get('session_id')

        if not session_id:
            # 自动生成session ID
            import time
            session_id = f"session_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        # 确保session ID唯一
        if hasattr(taxi_agent, 'context') and session_id in taxi_agent.context:
            # 如果已存在，清除旧的上下文
            del taxi_agent.context[session_id]

        logger.info(f"创建新会话: {session_id}")

        return jsonify({
            'session_id': session_id,
            'message': '新会话已创建',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"创建新会话失败: {e}")
        return jsonify({'error': '创建新会话失败'}), 500

@app.route('/api/sessions/<session_id>/info', methods=['GET'])
def get_session_info(session_id):
    """获取会话信息"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503

        info = {
            "session_id": session_id,
            "has_context": False,
            "message_count": 0,
            "last_activity": None,
            "timestamp": datetime.now().isoformat()
        }

        if hasattr(taxi_agent, 'context') and session_id in taxi_agent.context:
            context = taxi_agent.context[session_id]
            info["has_context"] = True
            info["message_count"] = len([msg for msg in context if msg.get("role") in ["user", "assistant"]])

        return jsonify(info)

    except Exception as e:
        logger.error(f"获取会话信息失败: {e}")
        return jsonify({'error': '获取会话信息失败'}), 500

@app.route('/api/sessions/<session_id>/reset', methods=['POST'])
def reset_session(session_id):
    """重置会话（清除会话历史）"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503

        # 清除打车Agent中的会话上下文
        if hasattr(taxi_agent, 'context') and session_id in taxi_agent.context:
            del taxi_agent.context[session_id]

        # 清除状态管理器中的会话相关状态
        if hasattr(taxi_agent, 'state'):
            taxi_agent.state.conversation_state.pop(session_id, None)
        
        logger.info(f"会话已清除: {session_id}")
        
        return jsonify({
            'message': '会话历史已清除',
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"清除会话失败: {e}")
        return jsonify({'error': '清除会话失败'}), 500

@app.route('/api/system/stats', methods=['GET'])
def get_system_stats():
    """获取系统统计信息"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503

        stats = {
            'timestamp': datetime.now().isoformat(),
            'system_status': 'healthy',
            'active_sessions': len(getattr(taxi_agent, 'context', {})),
        }

        # 尝试获取状态信息
        try:
            if hasattr(taxi_agent, 'state'):
                state = taxi_agent.state
                if hasattr(state, 'current_status'):
                    stats['agent_status'] = state.current_status
                if hasattr(state, 'execution_history'):
                    stats['execution_count'] = len(state.execution_history)
        except Exception as e:
            logger.warning(f"获取状态信息失败: {e}")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        return jsonify({'error': '获取系统统计失败'}), 500

@app.route('/api/tools/test', methods=['POST'])
def test_tools():
    """测试工具功能"""
    try:
        if not init_taxi_agent():
            return jsonify({'error': '打车系统暂不可用'}), 503
        
        data = request.get_json()
        tool_name = data.get('tool_name')
        tool_params = data.get('params', {})
        
        if not tool_name:
            return jsonify({'error': '必须指定工具名称'}), 400
        
        # 这里可以添加直接测试工具的逻辑
        # 目前返回一个模拟响应
        result = {
            'tool_name': tool_name,
            'params': tool_params,
            'result': '工具测试功能待实现',
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"工具测试失败: {e}")
        return jsonify({'error': '工具测试失败'}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    # 启动时初始化打车Agent
    logger.info("启动API服务器...")

    if init_taxi_agent():
        logger.info("打车Agent初始化成功，服务器准备就绪")
    else:
        logger.warning("打车Agent初始化失败，某些功能可能不可用")
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=8000,
        debug=True,
        threaded=True
    )
