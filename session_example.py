#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理使用示例
演示如何使用 session_id 功能和重新开始对话
"""

from taxi_agent_system import MainAgent


def basic_session_example():
    """基础会话使用示例"""
    print("🎯 基础会话使用示例")
    print("="*50)
    
    # 创建智能助手
    agent = MainAgent()
    
    # 方式1: 使用默认会话
    print("方式1: 使用默认会话")
    response1 = agent.process_user_input("你好")
    print(f"助手: {response1}")
    
    # 方式2: 指定会话ID
    print("\n方式2: 指定会话ID")
    response2 = agent.process_user_input("我想去西湖", "user_alice")
    print(f"助手: {response2}")
    
    # 方式3: 创建新会话
    print("\n方式3: 创建新会话")
    session_id = agent.start_new_session("my_custom_session")
    print(f"新会话ID: {session_id}")
    response3 = agent.process_user_input("北京有哪些星巴克？", session_id)
    print(f"助手: {response3}")


def multi_user_example():
    """多用户会话示例"""
    print("\n👥 多用户会话示例")
    print("="*50)
    
    agent = MainAgent()
    
    # 为不同用户创建独立会话
    alice_session = agent.start_new_session("alice")
    bob_session = agent.start_new_session("bob")
    
    print(f"Alice会话: {alice_session}")
    print(f"Bob会话: {bob_session}")
    
    # Alice的对话
    print("\n--- Alice的对话 ---")
    alice_response1 = agent.process_user_input("我想去西湖", alice_session)
    print(f"Alice: 我想去西湖")
    print(f"助手: {alice_response1}")
    
    alice_response2 = agent.process_user_input("那里怎么样？", alice_session)
    print(f"Alice: 那里怎么样？")
    print(f"助手: {alice_response2}")
    
    # Bob的对话
    print("\n--- Bob的对话 ---")
    bob_response1 = agent.process_user_input("找星巴克", bob_session)
    print(f"Bob: 找星巴克")
    print(f"助手: {bob_response1}")
    
    bob_response2 = agent.process_user_input("那里怎么走？", bob_session)  # Bob的"那里"不会与Alice混淆
    print(f"Bob: 那里怎么走？")
    print(f"助手: {bob_response2}")


def session_restart_example():
    """会话重启示例"""
    print("\n🔄 会话重启示例")
    print("="*50)
    
    agent = MainAgent()
    
    # 创建会话并进行对话
    session_id = agent.start_new_session("restart_demo")
    print(f"会话ID: {session_id}")
    
    # 第一轮对话
    print("\n--- 第一轮对话 ---")
    response1 = agent.process_user_input("我想去西湖", session_id)
    print(f"用户: 我想去西湖")
    print(f"助手: {response1}")
    
    response2 = agent.process_user_input("那里有什么好玩的？", session_id)
    print(f"用户: 那里有什么好玩的？")
    print(f"助手: {response2}")
    
    # 重启会话（清空上下文）
    print("\n--- 重启会话 ---")
    new_session_id = agent.start_new_session(session_id)  # 使用相同ID重启
    print(f"重启后会话ID: {new_session_id}")
    
    # 重启后的对话
    print("\n--- 重启后对话 ---")
    response3 = agent.process_user_input("那里有什么好玩的？", new_session_id)  # 这次不会理解"那里"
    print(f"用户: 那里有什么好玩的？")
    print(f"助手: {response3}")
    print("💡 注意：重启后系统不再理解'那里'的指代，因为上下文已清空")


def session_info_example():
    """会话信息查询示例"""
    print("\n📊 会话信息查询示例")
    print("="*50)
    
    agent = MainAgent()
    
    # 创建会话
    session_id = agent.start_new_session("info_demo")
    
    # 查看初始状态
    info = agent.get_session_info(session_id)
    print(f"初始会话信息: {info}")
    
    # 进行一些对话
    agent.process_user_input("你好", session_id)
    agent.process_user_input("我想去西湖", session_id)
    
    # 查看更新后的状态
    info = agent.get_session_info(session_id)
    print(f"对话后会话信息: {info}")


def practical_usage_tips():
    """实用使用技巧"""
    print("\n💡 实用使用技巧")
    print("="*50)
    
    agent = MainAgent()
    
    print("1. 为每个用户创建独立会话:")
    print("   session_id = agent.start_new_session(f'user_{user_id}')")
    
    print("\n2. 在Web应用中使用:")
    print("   # 用户登录时")
    print("   session_id = agent.start_new_session(f'web_user_{user_id}_{timestamp}')")
    print("   # 每次请求时")
    print("   response = agent.process_user_input(user_message, session_id)")
    
    print("\n3. 重新开始对话:")
    print("   # 用户点击'重新开始'按钮时")
    print("   new_session = agent.start_new_session(current_session_id)")
    
    print("\n4. 检查会话状态:")
    print("   info = agent.get_session_info(session_id)")
    print("   if info['message_count'] > 50:")
    print("       # 考虑重启会话以节省内存")
    
    print("\n5. 会话命名建议:")
    print("   - 用户会话: 'user_{user_id}'")
    print("   - 临时会话: 'temp_{timestamp}'")
    print("   - 测试会话: 'test_{test_name}'")


def main():
    """主函数"""
    print("🚀 会话管理使用示例")
    print("="*60)
    
    try:
        basic_session_example()
        multi_user_example()
        session_restart_example()
        session_info_example()
        practical_usage_tips()
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
    
    print("\n" + "="*60)
    print("✅ 会话管理示例完成")
    print("\n📝 总结:")
    print("- 使用 agent.start_new_session() 创建新会话")
    print("- 使用 agent.process_user_input(message, session_id) 处理消息")
    print("- 使用 agent.get_session_info(session_id) 查询会话状态")
    print("- 不同会话完全隔离，互不干扰")
    print("- 重启会话可以清空上下文，重新开始对话")


if __name__ == "__main__":
    main()
