"""
高德地图MCP工具集
提供地点到经纬度和地点到城市ID的转换功能
"""

import requests
import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class AmapMCPTools:
    """高德地图MCP工具类"""
    
    def __init__(self, api_key: str = None):
        """
        初始化高德地图工具
        
        Args:
            api_key: 高德地图API密钥，如果不提供则从环境变量AMAP_API_KEY获取
        """
        self.api_key = api_key or os.getenv("AMAP_API_KEY")
        if not self.api_key:
            raise ValueError("请提供高德地图API密钥，可通过参数或环境变量AMAP_API_KEY设置")
        
        # 高德地图API基础URL
        self.base_url = "https://restapi.amap.com/v3"
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def geocode_address(self, address: str, city: str = None) -> Dict:
        """
        地点名称转换为经纬度坐标（地理编码）
        
        Args:
            address: 地点名称或地址
            city: 城市名称，可选，用于提高搜索精度
            
        Returns:
            Dict: 包含经纬度信息的字典
            {
                "status": bool,
                "data": {
                    "longitude": float,
                    "latitude": float,
                    "formatted_address": str,
                    "province": str,
                    "city": str,
                    "district": str,
                    "adcode": str
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'address': address,
                'output': 'json'
            }
            
            if city:
                params['city'] = city
            
            # 发送请求
            url = f"{self.base_url}/geocode/geo"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            geocodes = result.get('geocodes', [])
            if not geocodes:
                return {
                    "status": False,
                    "error": f"未找到地点: {address}"
                }
            
            # 取第一个结果
            geocode = geocodes[0]
            location = geocode.get('location', '').split(',')
            
            if len(location) != 2:
                return {
                    "status": False,
                    "error": "坐标格式错误"
                }
            
            return {
                "status": True,
                "data": {
                    "longitude": float(location[0]),
                    "latitude": float(location[1]),
                    "formatted_address": geocode.get('formatted_address', ''),
                    "province": geocode.get('province', ''),
                    "city": geocode.get('city', ''),
                    "district": geocode.get('district', ''),
                    "adcode": geocode.get('adcode', '')
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def get_city_code(self, city_name: str) -> Dict:
        """
        获取城市的adcode（城市ID）
        
        Args:
            city_name: 城市名称
            
        Returns:
            Dict: 包含城市ID信息的字典
            {
                "status": bool,
                "data": {
                    "city_code": str,
                    "adcode": str,
                    "city_name": str,
                    "province": str,
                    "center": {
                        "longitude": float,
                        "latitude": float
                    }
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'keywords': city_name,
                'subdistrict': '0',  # 不返回下级行政区
                'output': 'json'
            }
            
            # 发送请求
            url = f"{self.base_url}/config/district"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            districts = result.get('districts', [])
            if not districts:
                return {
                    "status": False,
                    "error": f"未找到城市: {city_name}"
                }
            
            # 取第一个结果
            district = districts[0]
            center = district.get('center', '').split(',')
            
            center_coords = {}
            if len(center) == 2:
                center_coords = {
                    "longitude": float(center[0]),
                    "latitude": float(center[1])
                }
            
            return {
                "status": True,
                "data": {
                    "city_code": district.get('citycode', ''),
                    "adcode": district.get('adcode', ''),
                    "city_name": district.get('name', ''),
                    "province": district.get('name', ''),
                    "center": center_coords
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def batch_geocode(self, addresses: List[str], city: str = None) -> Dict:
        """
        批量地理编码
        
        Args:
            addresses: 地点名称列表
            city: 城市名称，可选
            
        Returns:
            Dict: 批量处理结果
        """
        results = []
        for address in addresses:
            result = self.geocode_address(address, city)
            results.append({
                "address": address,
                "result": result
            })
        
        return {
            "status": True,
            "data": {
                "total": len(addresses),
                "results": results
            }
        }
    
    def search_poi(self, keyword: str, city: str = None, types: str = None) -> Dict:
        """
        POI搜索（兴趣点搜索）

        Args:
            keyword: 搜索关键词
            city: 城市名称
            types: POI类型，如"餐饮服务|购物服务"

        Returns:
            Dict: POI搜索结果
        """
        try:
            params = {
                'key': self.api_key,
                'keywords': keyword,
                'output': 'json',
                'offset': 20,  # 每页记录数
                'page': 1,     # 当前页数
                'extensions': 'all'  # 返回详细信息
            }

            if city:
                params['city'] = city
            if types:
                params['types'] = types

            url = f"{self.base_url}/place/text"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()

            result = response.json()

            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }

            pois = result.get('pois', [])
            processed_pois = []

            for poi in pois:
                location = poi.get('location', '').split(',')
                poi_data = {
                    "name": poi.get('name', ''),
                    "address": poi.get('address', ''),
                    "type": poi.get('type', ''),
                    "tel": poi.get('tel', ''),
                    "longitude": float(location[0]) if len(location) >= 2 else None,
                    "latitude": float(location[1]) if len(location) >= 2 else None,
                    "adcode": poi.get('adcode', ''),
                    "cityname": poi.get('cityname', '')
                }
                processed_pois.append(poi_data)

            return {
                "status": True,
                "data": {
                    "count": result.get('count', 0),
                    "pois": processed_pois
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"POI搜索错误: {str(e)}"
            }

    def _simple_word_segment(self, text: str) -> List[str]:
        """
        改进的中文分词实现
        基于常见词汇和规则进行分词，优化了对复合词的处理

        Args:
            text: 待分词的文本

        Returns:
            List[str]: 分词结果列表
        """
        # 常见的建筑物、地点相关后缀词
        suffix_words = [
            "大厦", "大楼", "广场", "中心", "商场", "酒店", "宾馆", "医院", "学校", "银行",
            "公园", "体育馆", "图书馆", "博物馆", "剧院", "影院", "车站", "机场", "码头",
            "写字楼", "商务楼", "办公楼", "住宅", "小区", "花园", "别墅", "公寓", "社区",
            "科技园", "工业园", "产业园", "创业园", "孵化器", "总部", "基地", "园区",
            "软件园", "创新园", "高新区", "开发区", "新区", "新城", "城区", "区域"
        ]

        # 常见的机构、公司名称
        institution_words = [
            "北京大学", "清华大学", "中国人民大学", "北京师范大学", "北京理工大学",
            "中科院", "社科院", "中关村", "海淀", "朝阳", "东城", "西城", "丰台",
            "方正", "联想", "百度", "腾讯", "阿里", "华为", "小米", "京东",
            "国贸", "万达", "银泰", "华润", "恒大", "万科", "保利", "中海"
        ]

        # 常见的方位词
        direction_words = ["东", "南", "西", "北", "中", "上", "下", "前", "后", "左", "右", "内", "外"]

        # 数字词
        number_words = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "零"]

        # 合并所有词汇，按长度排序（长词优先）
        all_words = institution_words + suffix_words + number_words
        all_words.sort(key=len, reverse=True)

        segments = []
        remaining_text = text

        while remaining_text:
            matched = False

            # 1. 尝试匹配已知词汇（机构名、后缀词等）
            for word in all_words:
                if remaining_text.startswith(word):
                    segments.append(word)
                    remaining_text = remaining_text[len(word):]
                    matched = True
                    break

            if not matched:
                # 2. 处理连续的字母数字
                if remaining_text[0].isalnum():
                    word = ""
                    i = 0
                    while i < len(remaining_text) and remaining_text[i].isalnum():
                        word += remaining_text[i]
                        i += 1
                    segments.append(word)
                    remaining_text = remaining_text[i:]
                    matched = True

                # 3. 处理单个方位词
                elif remaining_text[0] in direction_words:
                    segments.append(remaining_text[0])
                    remaining_text = remaining_text[1:]
                    matched = True

                # 4. 处理连续的汉字（作为一个词组）
                else:
                    word = ""
                    i = 0
                    # 收集连续的汉字，直到遇到已知词汇的开头
                    while i < len(remaining_text):
                        char = remaining_text[i]

                        # 如果是字母数字或方位词，停止
                        if char.isalnum() or char in direction_words:
                            break

                        # 检查是否是已知词汇的开头
                        is_word_start = False
                        for known_word in all_words:
                            if remaining_text[i:].startswith(known_word):
                                is_word_start = True
                                break

                        if is_word_start and word:  # 如果已经有内容且遇到新词开头，停止
                            break

                        word += char
                        i += 1

                        # 限制单个词组的长度，避免过长
                        if len(word) >= 4:
                            break

                    if word:
                        segments.append(word)
                        remaining_text = remaining_text[len(word):]
                    else:
                        # 如果没有收集到内容，跳过当前字符
                        remaining_text = remaining_text[1:]

        # 过滤和优化结果
        filtered_segments = []
        for segment in segments:
            # 过滤掉单个无意义字符（除了数字、字母和方位词）
            if len(segment) > 1 or segment.isalnum() or segment in direction_words:
                filtered_segments.append(segment)

        # 如果分词结果为空或只有一个词，返回原始文本
        if not filtered_segments or (len(filtered_segments) == 1 and filtered_segments[0] == text):
            return [text]

        return filtered_segments

    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        计算两个坐标点之间的距离（米）
        使用Haversine公式计算球面距离

        Args:
            lat1, lng1: 第一个点的纬度和经度
            lat2, lng2: 第二个点的纬度和经度

        Returns:
            float: 距离（米）
        """
        import math

        # 将角度转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（米）
        r = 6371000

        return c * r

    def _calculate_confusion_score(self, pois: List[Dict]) -> float:
        """
        计算POI搜索结果的困惑度
        基于top3 POI之间的距离，距离越近困惑度越小

        Args:
            pois: POI列表

        Returns:
            float: 困惑度分数 (0-1)，0表示无困惑，1表示高度困惑
        """
        if len(pois) < 2:
            return 0.0  # 只有一个或没有结果，无困惑

        # 取前3个POI计算
        top_pois = pois[:min(3, len(pois))]

        # 提取有效坐标的POI
        valid_pois = []
        for poi in top_pois:
            if poi.get('longitude') and poi.get('latitude'):
                valid_pois.append(poi)

        if len(valid_pois) < 2:
            return 0.0  # 没有足够的有效坐标

        # 计算所有POI对之间的距离
        distances = []
        for i in range(len(valid_pois)):
            for j in range(i + 1, len(valid_pois)):
                poi1 = valid_pois[i]
                poi2 = valid_pois[j]

                distance = self._calculate_distance(
                    poi1['latitude'], poi1['longitude'],
                    poi2['latitude'], poi2['longitude']
                )
                distances.append(distance)

        if not distances:
            return 0.0

        # 计算平均距离
        avg_distance = sum(distances) / len(distances)

        # 困惑度计算：
        # - 300米以内：困惑度接近0（正常可接受）
        # - 300米-3000米：线性增长
        # - 3000米以上：困惑度接近1（高度困惑）

        if avg_distance <= 300:
            # 300米以内，困惑度很低
            confusion = avg_distance / 300 * 0.1  # 最大0.1
        elif avg_distance <= 3000:
            # 300-3000米，线性增长
            confusion = 0.1 + (avg_distance - 300) / 2700 * 0.8  # 0.1到0.9
        else:
            # 3000米以上，高度困惑
            confusion = 0.9 + min((avg_distance - 3000) / 10000, 0.1)  # 0.9到1.0

        return min(confusion, 1.0)

    def _get_confusion_level(self, confusion_score: float) -> str:
        """
        根据困惑度分数获取困惑度等级描述

        Args:
            confusion_score: 困惑度分数 (0-1)

        Returns:
            str: 困惑度等级描述
        """
        if confusion_score <= 0.2:
            return "很低"  # 300米以内，非常清晰
        elif confusion_score <= 0.4:
            return "低"    # 300-1000米左右，比较清晰
        elif confusion_score <= 0.6:
            return "中等"  # 1000-2000米左右，有一定困惑
        elif confusion_score <= 0.8:
            return "高"    # 2000-3000米左右，比较困惑
        else:
            return "很高"  # 3000米以上，高度困惑

    def search_poi_with_segmentation(self, keyword: str, city: str = None, types: str = None,
                                   require_all_segments: bool = True) -> Dict:
        """
        基于分词的POI搜索
        先对关键词进行分词，然后要求分词结果在POI名称中出现

        Args:
            keyword: 搜索关键词，如"方正大厦"
            city: 城市名称
            types: POI类型
            require_all_segments: 是否要求所有分词都在POI名称中出现

        Returns:
            Dict: 包含分词信息和过滤后的POI搜索结果
        """
        try:
            # 1. 对关键词进行分词
            segments = self._simple_word_segment(keyword)

            # 2. 进行原始POI搜索
            search_result = self.search_poi(keyword, city, types)

            if not search_result.get("status"):
                return search_result

            # 3. 根据分词结果过滤POI
            original_pois = search_result["data"]["pois"]
            filtered_pois = []

            for poi in original_pois:
                poi_name = poi.get("name", "")

                if require_all_segments:
                    # 要求所有分词都在POI名称中出现
                    match_count = 0
                    for segment in segments:
                        if segment in poi_name:
                            match_count += 1

                    # 所有分词都匹配才保留
                    if match_count == len(segments):
                        poi["match_segments"] = segments
                        poi["match_score"] = 1.0
                        filtered_pois.append(poi)
                else:
                    # 至少有一个分词在POI名称中出现
                    matched_segments = []
                    for segment in segments:
                        if segment in poi_name:
                            matched_segments.append(segment)

                    if matched_segments:
                        poi["match_segments"] = matched_segments
                        poi["match_score"] = len(matched_segments) / len(segments)
                        filtered_pois.append(poi)

            # 4. 按匹配分数排序
            filtered_pois.sort(key=lambda x: x.get("match_score", 0), reverse=True)

            # 5. 计算困惑度
            confusion_score = self._calculate_confusion_score(filtered_pois)

            return {
                "status": True,
                "data": {
                    "original_keyword": keyword,
                    "segments": segments,
                    "require_all_segments": require_all_segments,
                    "original_count": len(original_pois),
                    "filtered_count": len(filtered_pois),
                    "confusion_score": confusion_score,
                    "confusion_level": self._get_confusion_level(confusion_score),
                    "pois": filtered_pois
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"分词POI搜索错误: {str(e)}"
            }

    def reverse_geocode_poi(self, longitude: float, latitude: float, radius: int = 1000) -> Dict:
        """
        经纬度转POI（逆地理编码 + 周边搜索）
        输入经纬度，输出附近的POI名称

        Args:
            longitude: 经度
            latitude: 纬度
            radius: 搜索半径（米），默认1000米

        Returns:
            Dict: 包含附近POI信息的字典
            {
                "status": bool,
                "data": {
                    "location": {
                        "longitude": float,
                        "latitude": float
                    },
                    "formatted_address": str,
                    "nearby_pois": [
                        {
                            "name": str,
                            "address": str,
                            "type": str,
                            "distance": int,
                            "longitude": float,
                            "latitude": float
                        }
                    ]
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先进行逆地理编码获取地址信息
            regeo_params = {
                'key': self.api_key,
                'location': f"{longitude},{latitude}",
                'output': 'json',
                'radius': radius,
                'extensions': 'all'
            }

            regeo_url = f"{self.base_url}/geocode/regeo"
            regeo_response = requests.get(regeo_url, params=regeo_params, headers=self.headers, timeout=10)
            regeo_response.raise_for_status()

            regeo_result = regeo_response.json()

            if regeo_result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"逆地理编码错误: {regeo_result.get('info', '未知错误')}"
                }

            # 2. 进行周边POI搜索
            poi_params = {
                'key': self.api_key,
                'location': f"{longitude},{latitude}",
                'radius': radius,
                'output': 'json',
                'offset': 20,
                'page': 1,
                'extensions': 'all'
            }

            poi_url = f"{self.base_url}/place/around"
            poi_response = requests.get(poi_url, params=poi_params, headers=self.headers, timeout=10)
            poi_response.raise_for_status()

            poi_result = poi_response.json()

            if poi_result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"周边POI搜索错误: {poi_result.get('info', '未知错误')}"
                }

            # 处理逆地理编码结果
            regeocode = regeo_result.get('regeocode', {})
            formatted_address = regeocode.get('formatted_address', '')

            # 处理POI结果
            pois = poi_result.get('pois', [])
            nearby_pois = []

            for poi in pois:
                poi_location = poi.get('location', '').split(',')
                poi_data = {
                    "name": poi.get('name', ''),
                    "address": poi.get('address', ''),
                    "type": poi.get('type', ''),
                    "distance": int(poi.get('distance', 0)),
                    "longitude": float(poi_location[0]) if len(poi_location) >= 2 else None,
                    "latitude": float(poi_location[1]) if len(poi_location) >= 2 else None,
                    "tel": poi.get('tel', ''),
                    "business_area": poi.get('business_area', '')
                }
                nearby_pois.append(poi_data)

            return {
                "status": True,
                "data": {
                    "location": {
                        "longitude": longitude,
                        "latitude": latitude
                    },
                    "formatted_address": formatted_address,
                    "nearby_pois": nearby_pois,
                    "total_count": len(nearby_pois)
                }
            }

        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"经纬度转POI错误: {str(e)}"
            }

    def recommend_similar_poi(self, poi_name: str, city: str = None, radius: int = 2000) -> Dict:
        """
        POI推荐 - 根据模糊名称推荐相似POI
        输入一个模糊的POI名称，输出附近相似的POI

        Args:
            poi_name: POI名称，如"北京上地星巴克"
            city: 城市名称，可选
            radius: 搜索半径（米），默认2000米

        Returns:
            Dict: 包含推荐POI信息的字典
            {
                "status": bool,
                "data": {
                    "original_poi": {
                        "name": str,
                        "longitude": float,
                        "latitude": float,
                        "address": str
                    },
                    "similar_pois": [
                        {
                            "name": str,
                            "address": str,
                            "type": str,
                            "distance": int,
                            "longitude": float,
                            "latitude": float,
                            "similarity_score": float
                        }
                    ]
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先搜索原始POI获取其位置
            original_search = self.search_poi(poi_name, city)

            if not original_search.get("status") or not original_search.get("data", {}).get("pois"):
                return {
                    "status": False,
                    "error": f"未找到指定POI: {poi_name}"
                }

            # 取第一个搜索结果作为原始POI
            original_poi = original_search["data"]["pois"][0]
            original_longitude = original_poi["longitude"]
            original_latitude = original_poi["latitude"]

            if original_longitude is None or original_latitude is None:
                return {
                    "status": False,
                    "error": "原始POI坐标信息不完整"
                }

            # 2. 在原始POI周边搜索相似POI
            nearby_result = self.reverse_geocode_poi(original_longitude, original_latitude, radius)

            if not nearby_result.get("status"):
                return {
                    "status": False,
                    "error": f"周边搜索失败: {nearby_result.get('error', '未知错误')}"
                }

            # 3. 分析相似性并推荐
            nearby_pois = nearby_result["data"]["nearby_pois"]

            # 提取原始POI的关键词用于相似性分析
            original_keywords = self._extract_keywords(poi_name)
            original_type = original_poi.get("type", "")

            similar_pois = []
            for poi in nearby_pois:
                # 跳过原始POI本身
                if poi["name"] == original_poi["name"]:
                    continue

                # 计算相似性分数
                similarity_score = self._calculate_similarity(
                    poi, original_keywords, original_type
                )

                if similarity_score > 0.1:  # 相似性阈值
                    poi_with_score = poi.copy()
                    poi_with_score["similarity_score"] = similarity_score
                    similar_pois.append(poi_with_score)

            # 按相似性分数排序
            similar_pois.sort(key=lambda x: x["similarity_score"], reverse=True)

            # 限制返回数量
            similar_pois = similar_pois[:10]

            return {
                "status": True,
                "data": {
                    "original_poi": {
                        "name": original_poi["name"],
                        "longitude": original_longitude,
                        "latitude": original_latitude,
                        "address": original_poi["address"],
                        "type": original_poi["type"]
                    },
                    "similar_pois": similar_pois,
                    "total_count": len(similar_pois),
                    "search_radius": radius
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"POI推荐错误: {str(e)}"
            }

    def _extract_keywords(self, poi_name: str) -> List[str]:
        """从POI名称中提取关键词"""
        # 常见的品牌和类型关键词
        brand_keywords = [
            "星巴克", "麦当劳", "肯德基", "必胜客", "汉堡王", "德克士",
            "华为", "小米", "苹果", "三星", "OPPO", "vivo",
            "万达", "银泰", "大悦城", "万象城", "来福士", "IFS",
            "如家", "汉庭", "7天", "锦江", "华住", "亚朵",
            "中国银行", "工商银行", "建设银行", "农业银行", "招商银行"
        ]

        type_keywords = [
            "咖啡", "餐厅", "酒店", "银行", "商场", "超市", "药店",
            "医院", "学校", "公园", "地铁", "公交", "加油站"
        ]

        keywords = []
        poi_name_lower = poi_name.lower()

        # 提取品牌关键词
        for keyword in brand_keywords:
            if keyword in poi_name:
                keywords.append(keyword)

        # 提取类型关键词
        for keyword in type_keywords:
            if keyword in poi_name:
                keywords.append(keyword)

        return keywords

    def _calculate_similarity(self, poi: Dict, original_keywords: List[str], original_type: str) -> float:
        """计算POI相似性分数"""
        score = 0.0

        poi_name = poi.get("name", "").lower()
        poi_type = poi.get("type", "")

        # 1. 名称关键词匹配 (权重: 0.6)
        keyword_matches = 0
        for keyword in original_keywords:
            if keyword in poi_name:
                keyword_matches += 1

        if original_keywords:
            keyword_score = keyword_matches / len(original_keywords)
            score += keyword_score * 0.6

        # 2. 类型匹配 (权重: 0.3)
        if original_type and poi_type:
            # 简单的类型匹配
            if original_type == poi_type:
                score += 0.3
            elif any(t in poi_type for t in original_type.split('|')):
                score += 0.15

        # 3. 距离因子 (权重: 0.1)
        distance = poi.get("distance", 0)
        if distance > 0:
            # 距离越近分数越高，最大2000米
            distance_score = max(0, (2000 - distance) / 2000)
            score += distance_score * 0.1

        return min(score, 1.0)  # 确保分数不超过1.0

    def calculate_driving_route(self, origin_lng: float, origin_lat: float,
                               dest_lng: float, dest_lat: float, strategy: int = 10) -> Dict:
        """
        计算两个经纬度坐标之间的驾车导航距离和预估时间

        Args:
            origin_lng: 起点经度
            origin_lat: 起点纬度
            dest_lng: 终点经度
            dest_lat: 终点纬度
            strategy: 路径规划策略，默认10（躲避拥堵，路程较短）

        Returns:
            Dict: 包含导航距离和时间信息的字典
            {
                "status": bool,
                "data": {
                    "distance": int,        # 距离，单位：米
                    "duration": int,        # 时间，单位：秒
                    "tolls": float,         # 过路费，单位：元
                    "traffic_lights": int,  # 红绿灯数量
                    "restriction": int,     # 限行状态 0:无限行 1:有限行
                    "origin": str,          # 起点坐标
                    "destination": str,     # 终点坐标
                    "strategy": int         # 使用的策略
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'origin': f"{origin_lng},{origin_lat}",
                'destination': f"{dest_lng},{dest_lat}",
                'strategy': strategy,
                'extensions': 'base',  # 返回基本信息即可
                'output': 'json'
            }

            # 发送请求
            url = f"{self.base_url}/direction/driving"
            response = requests.get(url, params=params, headers=self.headers, timeout=15)
            response.raise_for_status()

            result = response.json()

            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德路径规划API错误: {result.get('info', '未知错误')}"
                }

            # 解析结果
            route = result.get('route', {})
            paths = route.get('paths', [])

            if not paths:
                return {
                    "status": False,
                    "error": "未找到可行的路径规划方案"
                }

            # 取第一个路径方案
            path = paths[0]

            return {
                "status": True,
                "data": {
                    "distance": int(path.get('distance', 0)),
                    "duration": int(path.get('duration', 0)),
                    "tolls": float(path.get('tolls', 0)),
                    "traffic_lights": int(path.get('traffic_lights', 0)),
                    "restriction": int(path.get('restriction', 0)),
                    "origin": f"{origin_lng},{origin_lat}",
                    "destination": f"{dest_lng},{dest_lat}",
                    "strategy": strategy
                }
            }

        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"路径规划计算错误: {str(e)}"
            }

    def calculate_poi_to_poi_route(self, origin_poi: str, dest_poi: str,
                                  origin_city: str = None, dest_city: str = None,
                                  strategy: int = 10) -> Dict:
        """
        计算两个POI名称之间的驾车导航距离和预估时间
        先将POI名称转换为坐标，再计算路径

        Args:
            origin_poi: 起点POI名称，如"北京天安门"
            dest_poi: 终点POI名称，如"北京西站"
            origin_city: 起点城市名称，可选，用于提高搜索精度
            dest_city: 终点城市名称，可选，用于提高搜索精度
            strategy: 路径规划策略，默认10（躲避拥堵，路程较短）

        Returns:
            Dict: 包含导航距离和时间信息的字典
            {
                "status": bool,
                "data": {
                    "distance": int,        # 距离，单位：米
                    "duration": int,        # 时间，单位：秒
                    "tolls": float,         # 过路费，单位：元
                    "traffic_lights": int,  # 红绿灯数量
                    "restriction": int,     # 限行状态 0:无限行 1:有限行
                    "origin_poi": str,      # 起点POI名称
                    "destination_poi": str, # 终点POI名称
                    "origin_address": str,  # 起点详细地址
                    "destination_address": str, # 终点详细地址
                    "origin_coords": str,   # 起点坐标
                    "destination_coords": str, # 终点坐标
                    "strategy": int         # 使用的策略
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 获取起点坐标
            origin_result = self.geocode_address(origin_poi, origin_city)
            if not origin_result.get("status"):
                return {
                    "status": False,
                    "error": f"起点地址解析失败: {origin_result.get('error', '未知错误')}"
                }

            origin_data = origin_result["data"]
            origin_lng = origin_data["longitude"]
            origin_lat = origin_data["latitude"]
            origin_address = origin_data["formatted_address"]

            # 2. 获取终点坐标
            dest_result = self.geocode_address(dest_poi, dest_city)
            if not dest_result.get("status"):
                return {
                    "status": False,
                    "error": f"终点地址解析失败: {dest_result.get('error', '未知错误')}"
                }

            dest_data = dest_result["data"]
            dest_lng = dest_data["longitude"]
            dest_lat = dest_data["latitude"]
            dest_address = dest_data["formatted_address"]

            # 3. 计算路径
            route_result = self.calculate_driving_route(
                origin_lng, origin_lat, dest_lng, dest_lat, strategy
            )

            if not route_result.get("status"):
                return {
                    "status": False,
                    "error": f"路径规划失败: {route_result.get('error', '未知错误')}"
                }

            # 4. 组合结果
            route_data = route_result["data"]
            route_data.update({
                "origin_poi": origin_poi,
                "destination_poi": dest_poi,
                "origin_address": origin_address,
                "destination_address": dest_address,
                "origin_coords": f"{origin_lng},{origin_lat}",
                "destination_coords": f"{dest_lng},{dest_lat}"
            })

            return {
                "status": True,
                "data": route_data
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"POI路径规划错误: {str(e)}"
            }

    def search_taxi_spots(self, location: str, city: str = None, radius: int = 1000) -> Dict:
        """
        搜索上车点推荐
        根据指定位置搜索附近适合打车的上车点

        Args:
            location: 位置名称或地址
            city: 城市名称，可选
            radius: 搜索半径（米），默认1000米

        Returns:
            Dict: 包含推荐上车点信息的字典
            {
                "status": bool,
                "data": {
                    "original_location": str,
                    "taxi_spots": [
                        {
                            "name": str,
                            "address": str,
                            "type": str,
                            "distance": int,
                            "longitude": float,
                            "latitude": float,
                            "suitability_score": float,  # 适合度评分 0-1
                            "reason": str  # 推荐理由
                        }
                    ],
                    "total_count": int
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先获取原始位置的坐标
            location_result = self.geocode_address(location, city)
            if not location_result.get("status"):
                return {
                    "status": False,
                    "error": f"位置解析失败: {location_result.get('error', '未知错误')}"
                }

            location_data = location_result["data"]
            longitude = location_data["longitude"]
            latitude = location_data["latitude"]

            # 2. 搜索附近的POI
            nearby_result = self.reverse_geocode_poi(longitude, latitude, radius)
            if not nearby_result.get("status"):
                return {
                    "status": False,
                    "error": f"附近POI搜索失败: {nearby_result.get('error', '未知错误')}"
                }

            # 3. 筛选适合打车的地点
            nearby_pois = nearby_result["data"]["nearby_pois"]
            taxi_spots = []

            # 定义适合打车的POI类型和关键词
            suitable_types = [
                "交通设施服务", "地铁站", "公交站", "火车站", "机场",
                "商务住宅", "购物服务", "餐饮服务", "住宿服务",
                "医疗保健服务", "科教文化服务"
            ]

            suitable_keywords = [
                "地铁", "公交", "车站", "机场", "码头", "港口",
                "酒店", "宾馆", "商场", "广场", "大厦", "写字楼",
                "医院", "学校", "银行", "政府", "办事处",
                "门口", "入口", "出口", "停车场"
            ]

            for poi in nearby_pois:
                poi_name = poi.get("name", "")
                poi_type = poi.get("type", "")
                poi_distance = poi.get("distance", 0)

                # 计算适合度评分
                suitability_score = 0.0
                reasons = []

                # 1. 类型匹配评分 (0.4权重)
                if any(t in poi_type for t in suitable_types):
                    suitability_score += 0.4
                    reasons.append("交通便利")

                # 2. 关键词匹配评分 (0.3权重)
                keyword_matches = sum(1 for keyword in suitable_keywords if keyword in poi_name)
                if keyword_matches > 0:
                    suitability_score += min(keyword_matches * 0.1, 0.3)
                    if "地铁" in poi_name or "车站" in poi_name:
                        reasons.append("公共交通枢纽")
                    elif "酒店" in poi_name or "大厦" in poi_name:
                        reasons.append("标志性建筑")
                    elif "门口" in poi_name or "入口" in poi_name:
                        reasons.append("便于停车")

                # 3. 距离评分 (0.3权重)
                if poi_distance <= 100:
                    suitability_score += 0.3
                    reasons.append("距离很近")
                elif poi_distance <= 300:
                    suitability_score += 0.2
                    reasons.append("距离较近")
                elif poi_distance <= 500:
                    suitability_score += 0.1

                # 只保留适合度评分大于0.2的地点
                if suitability_score >= 0.2:
                    taxi_spot = poi.copy()
                    taxi_spot["suitability_score"] = round(suitability_score, 2)
                    taxi_spot["reason"] = "、".join(reasons) if reasons else "一般推荐"
                    taxi_spots.append(taxi_spot)

            # 4. 按适合度评分排序，取前10个
            taxi_spots.sort(key=lambda x: (x["suitability_score"], -x["distance"]), reverse=True)
            taxi_spots = taxi_spots[:10]

            return {
                "status": True,
                "data": {
                    "original_location": location,
                    "original_coords": f"{longitude},{latitude}",
                    "taxi_spots": taxi_spots,
                    "total_count": len(taxi_spots),
                    "search_radius": radius
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"上车点搜索错误: {str(e)}"
            }

    def estimate_taxi_price(self, origin_poi: str, dest_poi: str,
                           origin_city: str = "北京市", dest_city: str = "北京市",
                           car_type: str = "经济型") -> Dict:
        """
        估算打车价格
        基于距离和时间估算不同车型的打车费用

        Args:
            origin_poi: 起点POI名称
            dest_poi: 终点POI名称
            origin_city: 起点城市名称，可选
            dest_city: 终点城市名称，可选
            car_type: 车型类型，如"经济型"、"舒适型"、"豪华型"

        Returns:
            Dict: 包含价格估算信息的字典
            {
                "status": bool,
                "data": {
                    "origin_poi": str,
                    "dest_poi": str,
                    "distance": int,        # 距离，单位：米
                    "duration": int,        # 时间，单位：秒
                    "car_type": str,        # 车型
                    "estimated_price": {
                        "base_price": float,    # 起步价
                        "distance_price": float, # 里程费
                        "time_price": float,    # 时长费
                        "total_price": float,   # 总价
                        "price_range": str      # 价格区间
                    },
                    "price_breakdown": str  # 价格明细说明
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先计算路径距离和时间
            route_result = self.calculate_poi_to_poi_route(
                origin_poi, dest_poi, origin_city, dest_city
            )

            if not route_result.get("status"):
                return {
                    "status": False,
                    "error": f"路径计算失败: {route_result.get('error', '未知错误')}"
                }

            route_data = route_result["data"]
            distance_m = route_data["distance"]  # 米
            duration_s = route_data["duration"]  # 秒

            distance_km = distance_m / 1000  # 转换为公里
            duration_min = duration_s / 60   # 转换为分钟

            # 2. 根据车型设置价格参数（参考北京地区价格）
            price_config = {
                "经济型": {
                    "base_price": 13.0,      # 起步价（3公里内）
                    "base_distance": 3.0,    # 起步距离（公里）
                    "distance_rate": 2.3,    # 里程费（元/公里）
                    "time_rate": 0.8,        # 时长费（元/分钟，低速时）
                    "night_rate": 1.2,       # 夜间加价倍数
                    "peak_rate": 1.3         # 高峰加价倍数
                },
                "舒适型": {
                    "base_price": 18.0,
                    "base_distance": 3.0,
                    "distance_rate": 3.2,
                    "time_rate": 1.0,
                    "night_rate": 1.2,
                    "peak_rate": 1.4
                },
                "豪华型": {
                    "base_price": 25.0,
                    "base_distance": 3.0,
                    "distance_rate": 4.5,
                    "time_rate": 1.5,
                    "night_rate": 1.2,
                    "peak_rate": 1.5
                }
            }

            config = price_config.get(car_type, price_config["经济型"])

            # 3. 计算价格
            # 起步价
            base_price = config["base_price"]

            # 里程费（超出起步距离的部分）
            extra_distance = max(0, distance_km - config["base_distance"])
            distance_price = extra_distance * config["distance_rate"]

            # 时长费（假设低速行驶时间，简化计算为总时间的30%）
            time_price = (duration_min * 0.3) * config["time_rate"]

            # 总价（不含加价）
            base_total = base_price + distance_price + time_price

            # 考虑可能的加价（这里简化处理，实际应根据时间判断）
            total_price = base_total

            # 4. 生成价格区间（考虑实际情况的波动）
            price_min = total_price * 0.9
            price_max = total_price * 1.3  # 考虑高峰、夜间等加价
            price_range = f"{(price_max+price_min)/2:.0f}元"
            # f"{price_min:.0f}-{price_max:.0f}元"

            # 5. 生成价格明细说明
            breakdown = f"起步价{base_price}元({config['base_distance']}公里内)"
            if extra_distance > 0:
                breakdown += f" + 里程费{distance_price:.1f}元({extra_distance:.1f}公里×{config['distance_rate']}元/公里)"
            if time_price > 0:
                breakdown += f" + 时长费{time_price:.1f}元"
            breakdown += f" = {total_price:.1f}元"

            return {
                "status": True,
                "data": {
                    "origin_poi": origin_poi,
                    "dest_poi": dest_poi,
                    "distance": distance_m,
                    "duration": duration_s,
                    "car_type": car_type,
                    "estimated_price": {
                        "base_price": round(base_price, 1),
                        "distance_price": round(distance_price, 1),
                        "time_price": round(time_price, 1),
                        "total_price": round(total_price, 1),
                        "price_range": price_range
                    },
                    "price_breakdown": breakdown,
                    "note": "价格仅供参考，实际费用可能因路况、时段等因素有所不同"
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"价格估算错误: {str(e)}"
            }


# 使用示例和测试函数
def test_amap_tools():
    """测试高德地图工具"""
    # 需要设置环境变量 AMAP_API_KEY
    try:
        amap = AmapMCPTools()
        result5 = mcp_search_poi("方正大厦", "北京市")
        # recommend_similar_poi("北京机场")
        print("result5:",result5)

        # result6 = amap.recommend_similar_poi("北京方正大厦")
        # print("result6:",result6)
        # 测试地理编码
        # print("=== 测试地理编码 ===")
        # result1 = amap.geocode_address("西湖", "杭州")
        # print(f"西湖坐标: {result1}")
        
        # # 测试城市代码获取
        # print("\n=== 测试城市代码 ===")
        # result2 = amap.get_city_code("杭州")
        # print(f"杭州城市代码: {result2}")
        
        # # 测试POI搜索
        # print("\n=== 测试POI搜索 ===")
        # result3 = amap.search_poi("星巴克", "杭州")
        # print(f"杭州星巴克: {result3}")

        # # 测试经纬度转POI
        # print("\n=== 测试经纬度转POI ===")
        # result4 = amap.reverse_geocode_poi(116.397428, 39.90923)  # 天安门坐标
        # print(f"天安门附近POI: {result4}")

        # # 测试POI推荐
        # print("\n=== 测试POI推荐 ===")
        # result5 = amap.recommend_similar_poi("北京上地星巴克")
        # print(f"上地星巴克附近推荐: {result5}")

    except Exception as e:
        print(f"测试失败: {e}")


# MCP工具函数定义，用于集成到现有系统
def mcp_geocode_address(address: str, city: str = None) -> Dict:
    """
    MCP工具：地点转经纬度
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.geocode_address(address, city)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_get_city_code(city_name: str) -> Dict:
    """
    MCP工具：获取城市ID
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.get_city_code(city_name)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_search_poi(keyword: str, city: str = None, types: str = None) -> Dict:
    """
    MCP工具：POI搜索（已升级为分词搜索）
    先对关键词进行分词，然后要求分词结果都在POI名称中出现，提供更精确的搜索结果
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        # 使用分词搜索替代原始搜索，默认要求所有分词都匹配
        result = amap.search_poi_with_segmentation(keyword, city, types, require_all_segments=True)

        if result.get("status"):
            # 转换为原始格式，保持向后兼容
            return {
                "status": True,
                "data": {
                    "count": result["data"]["filtered_count"],
                    "pois": result["data"]["pois"],
                    # 添加分词信息供调试使用
                    "segmentation_info": {
                        "original_keyword": result["data"]["original_keyword"],
                        "segments": result["data"]["segments"],
                        "original_count": result["data"]["original_count"],
                        "filtered_count": result["data"]["filtered_count"],
                        "confusion_score": result["data"]["confusion_score"],
                        "confusion_level": result["data"]["confusion_level"]
                    }
                }
            }
        else:
            return result

    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_search_poi_with_segmentation(keyword: str, city: str = None, types: str = None,
                                   require_all_segments: bool = True) -> Dict:
    """
    MCP工具：基于分词的POI搜索
    先对关键词进行分词，然后要求分词结果在POI名称中出现
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.search_poi_with_segmentation(keyword, city, types, require_all_segments)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_reverse_geocode_poi(longitude: float, latitude: float, radius: int = 1000) -> Dict:
    """
    MCP工具：经纬度转POI
    输入经纬度，输出附近的POI名称
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.reverse_geocode_poi(longitude, latitude, radius)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_recommend_similar_poi(poi_name: str, city: str = None, radius: int = 2000) -> Dict:
    """
    MCP工具：POI推荐
    根据模糊名称推荐相似POI
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.recommend_similar_poi(poi_name, city, radius)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_calculate_driving_route(origin_lng: float, origin_lat: float,
                               dest_lng: float, dest_lat: float, strategy: int = 10) -> Dict:
    """
    MCP工具：计算两个经纬度坐标之间的驾车导航距离和预估时间
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.calculate_driving_route(origin_lng, origin_lat, dest_lng, dest_lat, strategy)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_calculate_poi_to_poi_route(origin_poi: str, dest_poi: str,
                                  origin_city: str = None, dest_city: str = None,
                                  strategy: int = 10) -> Dict:
    """
    MCP工具：计算两个POI名称之间的驾车导航距离和预估时间
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.calculate_poi_to_poi_route(origin_poi, dest_poi, origin_city, dest_city, strategy)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_search_taxi_spots(location: str, city: str = None, radius: int = 1000) -> Dict:
    """
    MCP工具：搜索上车点推荐
    根据指定位置搜索附近适合打车的上车点
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.search_taxi_spots(location, city, radius)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_estimate_taxi_price(origin_poi: str, dest_poi: str,
                           origin_city: str = None, dest_city: str = None,
                           car_type: str = "经济型") -> Dict:
    """
    MCP工具：估算打车价格
    基于距离和时间估算不同车型的打车费用
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.estimate_taxi_price(origin_poi, dest_poi, origin_city, dest_city, car_type)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


# 高德地图工具的function calling定义
AMAP_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "mcp_geocode_address",
            "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
            "parameters": {
                "type": "object",
                "properties": {
                    "address": {
                        "type": "string",
                        "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                    }
                },
                "required": ["address"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_get_city_code",
            "description": "获取城市的adcode（城市ID）和相关信息。",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {
                        "type": "string",
                        "description": "城市名称，如'杭州'、'北京'、'上海'"
                    }
                },
                "required": ["city_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search_poi",
            "description": "智能POI搜索（已升级）：先对关键词进行分词（如'方正大厦'分为'方正'+'大厦'），然后要求分词结果都在POI名称中出现，提供更精确的搜索结果。适用于搜索商店、餐厅、景点、建筑物等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词，如'方正大厦'、'海淀医院'、'万达广场'等"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于限定搜索范围"
                    },
                    "types": {
                        "type": "string",
                        "description": "POI类型，可选，如'餐饮服务'、'购物服务'等"
                    }
                },
                "required": ["keyword"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search_poi_with_segmentation",
            "description": "基于分词的POI搜索。先对关键词进行分词（如'方正大厦'分为'方正'+'大厦'），然后要求分词结果都在POI名称中出现，提供更精确的搜索结果。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词，如'方正大厦'、'海淀医院'等"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于限定搜索范围"
                    },
                    "types": {
                        "type": "string",
                        "description": "POI类型，可选，如'餐饮服务'、'购物服务'等"
                    },
                    "require_all_segments": {
                        "type": "boolean",
                        "description": "是否要求所有分词都在POI名称中出现，默认true。false时只要有部分分词匹配即可"
                    }
                },
                "required": ["keyword"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_reverse_geocode_poi",
            "description": "根据经纬度坐标查找附近的POI（兴趣点）。输入经纬度，输出附近的商店、餐厅、景点等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "longitude": {
                        "type": "number",
                        "description": "经度，如116.397428"
                    },
                    "latitude": {
                        "type": "number",
                        "description": "纬度，如39.90923"
                    },
                    "radius": {
                        "type": "integer",
                        "description": "搜索半径（米），默认1000米，最大3000米"
                    }
                },
                "required": ["longitude", "latitude"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_recommend_similar_poi",
            "description": "根据POI名称推荐附近相似的POI。例如输入'北京上地星巴克'，会推荐上地星巴克附近的咖啡店，而不是北京所有的星巴克。",
            "parameters": {
                "type": "object",
                "properties": {
                    "poi_name": {
                        "type": "string",
                        "description": "POI名称，可以是模糊名称，如'北京上地星巴克'、'杭州西湖银泰'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度"
                    },
                    "radius": {
                        "type": "integer",
                        "description": "推荐范围半径（米），默认2000米"
                    }
                },
                "required": ["poi_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_calculate_driving_route",
            "description": "计算两个经纬度坐标之间的驾车导航距离和预估时间。返回距离、时间、过路费、红绿灯数量等信息。",
            "parameters": {
                "type": "object",
                "properties": {
                    "origin_lng": {
                        "type": "number",
                        "description": "起点经度，如116.397428"
                    },
                    "origin_lat": {
                        "type": "number",
                        "description": "起点纬度，如39.90923"
                    },
                    "dest_lng": {
                        "type": "number",
                        "description": "终点经度，如116.465302"
                    },
                    "dest_lat": {
                        "type": "number",
                        "description": "终点纬度，如40.004717"
                    },
                    "strategy": {
                        "type": "integer",
                        "description": "路径规划策略，默认10（躲避拥堵，路程较短）。可选值：10-20为多策略，0-9为单策略"
                    }
                },
                "required": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_calculate_poi_to_poi_route",
            "description": "计算两个POI名称之间的驾车导航距离和预估时间。先将POI名称转换为坐标，再计算路径。适用于'从北京天安门到北京西站'这类查询。",
            "parameters": {
                "type": "object",
                "properties": {
                    "origin_poi": {
                        "type": "string",
                        "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
                    },
                    "dest_poi": {
                        "type": "string",
                        "description": "终点POI名称，如'北京西站'、'杭州东站'"
                    },
                    "origin_city": {
                        "type": "string",
                        "description": "起点城市名称，可选，用于提高搜索精度"
                    },
                    "dest_city": {
                        "type": "string",
                        "description": "终点城市名称，可选，用于提高搜索精度"
                    },
                    "strategy": {
                        "type": "integer",
                        "description": "路径规划策略，默认10（躲避拥堵，路程较短）"
                    }
                },
                "required": ["origin_poi", "dest_poi"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search_taxi_spots",
            "description": "搜索上车点推荐。根据指定位置搜索附近适合打车的上车点，如地铁站、酒店、商场等交通便利的地点。",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "位置名称或地址，如'北京大学'、'方正大厦'、'海淀医院'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度"
                    },
                    "radius": {
                        "type": "integer",
                        "description": "搜索半径（米），默认1000米，建议500-2000米"
                    }
                },
                "required": ["location"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_estimate_taxi_price",
            "description": "估算打车价格。基于起点终点距离和时间，估算不同车型的打车费用，包括起步价、里程费、时长费等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "origin_poi": {
                        "type": "string",
                        "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
                    },
                    "dest_poi": {
                        "type": "string",
                        "description": "终点POI名称，如'北京西站'、'杭州东站'"
                    },
                    "origin_city": {
                        "type": "string",
                        "description": "起点城市名称，可选，用于提高搜索精度"
                    },
                    "dest_city": {
                        "type": "string",
                        "description": "终点城市名称，可选，用于提高搜索精度"
                    },
                    "car_type": {
                        "type": "string",
                        "description": "车型类型，可选值：'经济型'、'舒适型'、'豪华型'，默认'经济型'"
                    }
                },
                "required": ["origin_poi", "dest_poi"]
            }
        }
    }
]


if __name__ == "__main__":
    test_amap_tools()
