#!/usr/bin/env python3
"""
测试None参数值的验证逻辑
验证当入参为None时，validation_result应该返回{'can_execute': False}
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_test_header(title):
    """打印测试标题"""
    print("\n" + "🎯" + "="*78)
    print(f"  {title}")
    print("🎯" + "="*78)

def test_none_parameter_cases():
    """测试None参数的各种情况"""
    print_test_header("None参数验证测试")
    
    print("📋 测试目标:")
    print("   1. 验证入参为None时不能通过验证")
    print("   2. 测试不同的None值情况")
    print("   3. 确保validation_result返回{'can_execute': False}")
    
    agent = MainAgent()
    session_id = f"none_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试用例：模拟"我要打车从方正大厦走"的情况
    test_cases = [
        {
            "description": "end_place为None的情况",
            "user_input": "我要打车从方正大厦走",
            "expected_function_args": {
                "start_place": "方正大厦",
                "end_place": None  # 这里应该是None
            },
            "expected_result": "validation_result应该是{'can_execute': False}"
        },
        {
            "description": "start_place为None的情况", 
            "user_input": "我要打车去大兴机场",
            "expected_function_args": {
                "start_place": None,  # 这里应该是None
                "end_place": "大兴机场"
            },
            "expected_result": "validation_result应该是{'can_execute': False}"
        },
        {
            "description": "两个参数都为None的情况",
            "user_input": "我要打车",
            "expected_function_args": {
                "start_place": None,
                "end_place": None
            },
            "expected_result": "validation_result应该是{'can_execute': False}"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}")
        print(f"📝 描述: {test_case['description']}")
        print(f"👤 用户输入: {test_case['user_input']}")
        print(f"🎯 预期结果: {test_case['expected_result']}")
        
        print(f"\n{'='*60}")
        print(f"开始处理...")
        print(f"{'='*60}")
        
        try:
            response = agent.process_user_input(test_case['user_input'], session_id)
            
            print(f"\n🤖 系统回复: {response}")
            
            # 检查最新的action状态
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    latest_action = hist[-1]
                    can_execute = latest_action.get("can_execute", False)
                    
                    print(f"\n📊 验证结果分析:")
                    print(f"   can_execute: {can_execute}")
                    
                    if not can_execute:
                        print(f"   ✅ 正确 - None参数被正确识别，不能执行")
                        
                        # 分析具体原因
                        action_type = latest_action.get("action_type", "")
                        if action_type == "provide_parameters":
                            missing_params = latest_action.get("missing_parameters", [])
                            print(f"   📋 缺少参数: {missing_params}")
                        elif action_type == "fix_parameters":
                            validation_errors = latest_action.get("validation_errors", [])
                            print(f"   ⚠️  验证错误: {validation_errors}")
                        
                        error_msg = latest_action.get("error", "")
                        if error_msg:
                            print(f"   📝 错误信息: {error_msg}")
                    else:
                        print(f"   ❌ 错误 - None参数未被正确识别，仍然可以执行")
                        print(f"   🔍 需要检查参数验证逻辑")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'.'*80}")
        if i < len(test_cases):
            input("按回车继续下一个测试用例...")

def test_direct_validation():
    """直接测试验证函数"""
    print_test_header("直接验证函数测试")
    
    print("📋 测试目标:")
    print("   1. 直接调用验证函数测试None参数")
    print("   2. 验证_validate_function_by_type的行为")
    
    agent = MainAgent()
    session_id = f"direct_test_{datetime.now().strftime('%H%M%S')}"
    
    # 直接测试验证函数
    test_scenarios = [
        {
            "name": "end_place为None",
            "function_name": "call_taxi_service",
            "function_args": {
                "start_place": "方正大厦",
                "end_place": None
            }
        },
        {
            "name": "start_place为None",
            "function_name": "call_taxi_service", 
            "function_args": {
                "start_place": None,
                "end_place": "大兴机场"
            }
        },
        {
            "name": "两个参数都为None",
            "function_name": "call_taxi_service",
            "function_args": {
                "start_place": None,
                "end_place": None
            }
        },
        {
            "name": "空字符串参数",
            "function_name": "call_taxi_service",
            "function_args": {
                "start_place": "方正大厦",
                "end_place": ""
            }
        },
        {
            "name": "正常参数",
            "function_name": "call_taxi_service",
            "function_args": {
                "start_place": "方正大厦",
                "end_place": "大兴机场"
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🧪 直接验证测试 {i}")
        print(f"📝 场景: {scenario['name']}")
        print(f"🔧 函数: {scenario['function_name']}")
        print(f"📊 参数: {scenario['function_args']}")
        
        try:
            # 直接调用验证函数
            validation_result = agent.enhanced_agent._validate_function_by_type(
                scenario['function_name'],
                scenario['function_args'], 
                session_id
            )
            
            can_execute = validation_result.get("can_execute", False)
            
            print(f"\n📊 验证结果:")
            print(f"   can_execute: {can_execute}")
            print(f"   status: {validation_result.get('status', 'N/A')}")
            
            if not can_execute:
                print(f"   ✅ 正确 - 参数验证失败，不能执行")
                error_msg = validation_result.get("error", "")
                if error_msg:
                    print(f"   📝 错误信息: {error_msg}")
                
                missing_params = validation_result.get("missing_parameters", [])
                if missing_params:
                    print(f"   📋 缺少参数: {missing_params}")
                    
                action_type = validation_result.get("action_type", "")
                if action_type:
                    print(f"   🔄 需要操作: {action_type}")
            else:
                print(f"   ✅ 正确 - 参数验证通过，可以执行")
            
            # 显示完整的验证结果
            print(f"\n🔍 完整验证结果:")
            for key, value in validation_result.items():
                if key not in ["can_execute", "status", "error"]:
                    print(f"   {key}: {value}")
                    
        except Exception as e:
            print(f"❌ 直接验证出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'.'*60}")
        if i < len(test_scenarios):
            input("按回车继续下一个测试...")

def test_edge_cases():
    """测试边界情况"""
    print_test_header("边界情况测试")
    
    print("📋 测试目标:")
    print("   1. 测试各种边界值情况")
    print("   2. 验证参数检查的完整性")
    
    agent = MainAgent()
    session_id = f"edge_test_{datetime.now().strftime('%H%M%S')}"
    
    edge_cases = [
        {
            "name": "参数值为False",
            "args": {"start_place": "方正大厦", "end_place": False}
        },
        {
            "name": "参数值为0",
            "args": {"start_place": "方正大厦", "end_place": 0}
        },
        {
            "name": "参数值为空列表",
            "args": {"start_place": "方正大厦", "end_place": []}
        },
        {
            "name": "参数值为空字典",
            "args": {"start_place": "方正大厦", "end_place": {}}
        },
        {
            "name": "参数值为只有空格的字符串",
            "args": {"start_place": "方正大厦", "end_place": "   "}
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n🧪 边界测试 {i}")
        print(f"📝 场景: {case['name']}")
        print(f"📊 参数: {case['args']}")
        
        try:
            validation_result = agent.enhanced_agent._validate_function_by_type(
                "call_taxi_service",
                case['args'],
                session_id
            )
            
            can_execute = validation_result.get("can_execute", False)
            print(f"\n📊 验证结果: {'✅ 可执行' if can_execute else '❌ 不可执行'}")
            
            if not can_execute:
                error_msg = validation_result.get("error", "")
                print(f"   📝 错误信息: {error_msg}")
                
        except Exception as e:
            print(f"❌ 边界测试出错: {str(e)}")
        
        print(f"\n{'.'*40}")

def main():
    """主函数"""
    print("🔧 None参数验证测试")
    print("验证当入参为None时，validation_result返回{'can_execute': False}")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. None参数验证测试")
    print("2. 直接验证函数测试")
    print("3. 边界情况测试")
    print("4. 全部测试")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            test_none_parameter_cases()
            break
        elif choice == "2":
            test_direct_validation()
            break
        elif choice == "3":
            test_edge_cases()
            break
        elif choice == "4":
            test_none_parameter_cases()
            test_direct_validation()
            test_edge_cases()
            break
        elif choice == "5":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-5")

if __name__ == "__main__":
    main()
