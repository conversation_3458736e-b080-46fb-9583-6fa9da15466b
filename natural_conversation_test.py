#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然对话测试脚本
测试优化后的口语化、智能化对话效果
"""

import time
from datetime import datetime
from taxi_agent_system import MainAgent


class NaturalConversationTester:
    """自然对话测试器"""
    
    def __init__(self):
        self.agent = MainAgent()
        # 设置测试环境 - 模拟用户在方正大厦
        self.agent.update_location_context(116.306345, 40.040377)  # 方正大厦坐标
        
    def test_scenario_1(self):
        """测试场景1：简洁的打车流程"""
        print("🎯 场景1：期望的简洁打车流程")
        print("="*60)
        
        session_id = self.agent.start_new_session("scenario_1")
        
        conversation = [
            ("打车", "期望回复：好啊，你要去哪？"),
            ("去机场", "期望回复：大兴还是北京国际机场？"),
            ("大兴", "期望回复：从上地五街方正大厦到大兴机场，预计60分钟，120元可以么？")
        ]
        
        for i, (user_input, expected) in enumerate(conversation, 1):
            print(f"\n第{i}轮:")
            print(f"👤 用户: {user_input}")
            print(f"💭 {expected}")
            
            try:
                response = self.agent.process_message(user_input, session_id)
                print(f"🤖 实际回复: {response}")
                
                # 简单的效果评估
                if len(response) <= 50 and any(keyword in response for keyword in ["哪", "去", "机场", "大兴", "首都"]):
                    print("✅ 回复简洁且相关")
                else:
                    print("⚠️  回复可能过长或不够直接")
                    
            except Exception as e:
                print(f"❌ 错误: {e}")
            
            print("-" * 40)
            time.sleep(1)
    
    def test_scenario_2(self):
        """测试场景2：时间冲突检测"""
        print("\n🎯 场景2：时间冲突检测和智能建议")
        print("="*60)
        
        session_id = self.agent.start_new_session("scenario_2")
        
        # 模拟当前时间为下午4点
        current_time = datetime.now().strftime("%H:%M")
        
        user_input = "帮我打个车从方正大厦去大兴机场，今天晚上8点的飞机"
        expected = f"期望回复：从上地五街方正大厦到大兴机场，预计60分钟，120元，不过现在才{current_time}，您确定要现在打车么，还是确定下预约时间？"
        
        print(f"👤 用户: {user_input}")
        print(f"💭 {expected}")
        
        try:
            response = self.agent.process_message(user_input, session_id)
            print(f"🤖 实际回复: {response}")
            
            # 检查是否包含时间冲突提醒
            if any(keyword in response for keyword in ["现在", "太早", "预约", "时间"]):
                print("✅ 成功检测到时间冲突")
            else:
                print("⚠️  未检测到时间冲突")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    def test_natural_flow(self):
        """测试自然对话流程"""
        print("\n🎯 场景3：自然对话流程测试")
        print("="*60)
        
        session_id = self.agent.start_new_session("natural_flow")
        
        # 测试多种自然表达方式
        test_cases = [
            {
                "input": "我要去首都机场",
                "expectation": "应该自动识别起点为当前位置，询问确认或直接提供方案"
            },
            {
                "input": "打个车",
                "expectation": "应该简洁地问去哪里"
            },
            {
                "input": "去西单",
                "expectation": "应该提供从当前位置到西单的方案"
            },
            {
                "input": "明天早上7点去机场",
                "expectation": "应该询问是否需要预约"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试{i}:")
            print(f"👤 用户: {case['input']}")
            print(f"💭 期望: {case['expectation']}")
            
            try:
                response = self.agent.process_message(case['input'], session_id)
                print(f"🤖 实际回复: {response}")
                
                # 评估回复质量
                response_length = len(response)
                if response_length <= 60:
                    print("✅ 回复简洁")
                else:
                    print(f"⚠️  回复较长 ({response_length}字)")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
            
            print("-" * 40)
            time.sleep(1)
    
    def test_context_awareness(self):
        """测试上下文感知能力"""
        print("\n🎯 场景4：上下文感知测试")
        print("="*60)
        
        session_id = self.agent.start_new_session("context_test")
        
        # 测试上下文记忆和智能补全
        conversation_flow = [
            ("我要去机场", "应该询问哪个机场"),
            ("大兴", "应该记住目的地，提供完整方案"),
            ("换成首都机场", "应该更新目的地"),
            ("多少钱？", "应该基于新的目的地回答"),
            ("太贵了", "应该提供其他建议")
        ]
        
        for i, (user_input, expectation) in enumerate(conversation_flow, 1):
            print(f"\n第{i}轮:")
            print(f"👤 用户: {user_input}")
            print(f"💭 期望: {expectation}")
            
            try:
                response = self.agent.process_message(user_input, session_id)
                print(f"🤖 实际回复: {response}")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
            
            print("-" * 40)
            time.sleep(1)
    
    def test_conflict_detection(self):
        """测试冲突检测能力"""
        print("\n🎯 场景5：冲突检测测试")
        print("="*60)
        
        session_id = self.agent.start_new_session("conflict_test")
        
        # 测试各种冲突场景
        conflict_cases = [
            {
                "input": "现在去机场，6点的飞机",
                "conflict_type": "时间过早",
                "expectation": "应该提醒现在出发太早"
            },
            {
                "input": "去火星",
                "conflict_type": "无效目的地",
                "expectation": "应该提醒目的地无效"
            },
            {
                "input": "1点去机场，凌晨3点的飞机",
                "conflict_type": "深夜出行",
                "expectation": "应该确认深夜出行"
            }
        ]
        
        for i, case in enumerate(conflict_cases, 1):
            print(f"\n冲突测试{i} - {case['conflict_type']}:")
            print(f"👤 用户: {case['input']}")
            print(f"💭 期望: {case['expectation']}")
            
            try:
                response = self.agent.process_message(case['input'], session_id)
                print(f"🤖 实际回复: {response}")
                
                # 检查是否检测到冲突
                conflict_keywords = ["太早", "无效", "确认", "注意", "提醒"]
                if any(keyword in response for keyword in conflict_keywords):
                    print("✅ 成功检测到冲突")
                else:
                    print("⚠️  未明确检测到冲突")
                
            except Exception as e:
                print(f"❌ 错误: {e}")
            
            print("-" * 40)
            time.sleep(1)
    
    def evaluate_conversation_quality(self, response: str) -> dict:
        """评估对话质量"""
        evaluation = {
            "length_score": 0,
            "naturalness_score": 0,
            "relevance_score": 0,
            "total_score": 0
        }
        
        # 长度评分 (简洁性)
        length = len(response)
        if length <= 30:
            evaluation["length_score"] = 10
        elif length <= 50:
            evaluation["length_score"] = 8
        elif length <= 80:
            evaluation["length_score"] = 6
        else:
            evaluation["length_score"] = 4
        
        # 自然度评分 (口语化程度)
        natural_words = ["哪", "吗", "呢", "啊", "哦", "好的", "可以"]
        formal_words = ["请问", "您好", "非常", "感谢"]
        
        natural_count = sum(1 for word in natural_words if word in response)
        formal_count = sum(1 for word in formal_words if word in response)
        
        if natural_count > formal_count:
            evaluation["naturalness_score"] = 10
        elif natural_count == formal_count:
            evaluation["naturalness_score"] = 7
        else:
            evaluation["naturalness_score"] = 5
        
        # 相关性评分
        relevant_keywords = ["机场", "打车", "去", "从", "时间", "费用", "预约"]
        relevance_count = sum(1 for keyword in relevant_keywords if keyword in response)
        
        if relevance_count >= 2:
            evaluation["relevance_score"] = 10
        elif relevance_count == 1:
            evaluation["relevance_score"] = 7
        else:
            evaluation["relevance_score"] = 5
        
        # 总分
        evaluation["total_score"] = (
            evaluation["length_score"] + 
            evaluation["naturalness_score"] + 
            evaluation["relevance_score"]
        ) / 3
        
        return evaluation


def main():
    """主测试函数"""
    print("🚀 自然对话效果测试开始")
    print("="*60)
    print("测试目标：")
    print("1. 对话要简洁、口语化")
    print("2. 智能利用环境信息")
    print("3. 检测时间等冲突")
    print("4. 自然的对话流程")
    print("="*60)
    
    tester = NaturalConversationTester()
    
    try:
        # 运行各项测试
        tester.test_scenario_1()
        tester.test_scenario_2()
        tester.test_natural_flow()
        tester.test_context_awareness()
        tester.test_conflict_detection()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    
    print("\n" + "="*60)
    print("🏁 自然对话测试完成")
    print("="*60)
    print("\n📋 测试总结:")
    print("✅ 简洁性测试 - 检查回复是否简短有力")
    print("✅ 口语化测试 - 检查是否像真人对话")
    print("✅ 智能补全测试 - 检查环境信息利用")
    print("✅ 冲突检测测试 - 检查时间等冲突识别")
    print("✅ 上下文感知测试 - 检查对话连贯性")


if __name__ == "__main__":
    main()
