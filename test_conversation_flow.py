#!/usr/bin/env python3
"""
测试指定的对话流程：
用户: 打车
助手: 好啊，你要去哪？
用户: 去机场  
助手: 大兴还是北京国际机场？
用户: 大兴
助手: 从上地五街方正大厦到大兴机场，预计60分钟，120元可以吗？

演示参数验证的4状态变化和容错率低的参数确认机制
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_conversation_header(round_num, user_input, expected_response):
    """打印对话轮次信息"""
    print("\n" + "="*70)
    print(f"  第{round_num}轮对话")
    print("="*70)
    print(f"👤 用户: {user_input}")
    print(f"🎯 预期回复: {expected_response}")
    print("-"*70)

def print_parameter_analysis(agent):
    """分析并打印参数状态"""
    if not hasattr(agent.enhanced_agent, 'parameter_manager'):
        print("📊 参数管理器未初始化")
        return
    
    try:
        # 检查call_taxi_service函数的参数状态
        manager = agent.enhanced_agent.parameter_manager
        if "call_taxi_service" in manager.function_parameters:
            status = manager.get_function_status("call_taxi_service")
            if status.get("status"):
                print("\n📊 参数状态分析:")
                parameters = status.get("parameters", {})
                
                for param_name, param_info in parameters.items():
                    state = param_info.get("state", "未知")
                    value = param_info.get("value", "无")
                    confusion_score = param_info.get("confusion_score")
                    
                    state_icons = {
                        "未填写": "⚪",
                        "未校验": "🟡", 
                        "已校验待确认": "🟠",
                        "已确认": "🟢"
                    }
                    icon = state_icons.get(state, "❓")
                    
                    print(f"   {icon} {param_name}: {state} (值: {value})")
                    if confusion_score is not None:
                        level = "低" if confusion_score <= 0.2 else "中" if confusion_score <= 0.5 else "高"
                        print(f"      困惑度: {confusion_score:.2f} ({level})")
                
                execution_check = status.get("execution_check", {})
                can_execute = execution_check.get("can_execute", False)
                fault_tolerance = execution_check.get("fault_tolerance", "未知")
                
                print(f"\n   🔧 容错率: {fault_tolerance}")
                print(f"   🚀 可执行: {'✅' if can_execute else '❌'}")
                
                if not can_execute:
                    missing = execution_check.get("missing_required", [])
                    unvalidated = execution_check.get("unvalidated_params", [])
                    unconfirmed = execution_check.get("unconfirmed_params", [])
                    
                    if missing:
                        print(f"      ⚠️  缺少必填参数: {missing}")
                    if unvalidated:
                        print(f"      ⚠️  未校验参数: {unvalidated}")
                    if unconfirmed:
                        print(f"      ⚠️  未确认参数: {unconfirmed}")
                        print("      💡 低容错率函数需要用户确认")
        else:
            print("📊 尚未初始化call_taxi_service参数")
            
    except Exception as e:
        print(f"📊 参数分析出错: {str(e)}")

def test_specific_conversation():
    """测试指定的对话流程"""
    print("🎯 测试目标对话流程")
    print("演示参数从'未填写'到'已确认'的完整变化过程")
    print("确保容错率低的参数都经过用户确认")
    
    # 初始化Agent
    print("\n🚀 初始化MainAgent...")
    agent = MainAgent()
    session_id = f"test_{datetime.now().strftime('%H%M%S')}"
    
    # 定义测试对话序列
    conversations = [
        {
            "user_input": "打车",
            "expected_response": "好啊，你要去哪？",
            "analysis": "用户发起打车请求，系统询问目的地。参数处于'未填写'状态。"
        },
        {
            "user_input": "去机场", 
            "expected_response": "大兴还是北京国际机场？",
            "analysis": "用户说去机场，但'机场'太模糊，系统询问具体机场。参数可能处于'未校验'状态。"
        },
        {
            "user_input": "大兴",
            "expected_response": "从上地五街方正大厦到大兴机场，预计60分钟，120元可以吗？",
            "analysis": "用户明确大兴机场，系统验证参数、计算路线价格，请求确认。参数变为'已校验待确认'状态。"
        },
        {
            "user_input": "确认",
            "expected_response": "好的，已为您叫车...",
            "analysis": "用户确认参数，系统执行打车服务。参数变为'已确认'状态，可以安全执行。"
        }
    ]
    
    # 执行对话测试
    for i, conv in enumerate(conversations, 1):
        print_conversation_header(i, conv["user_input"], conv["expected_response"])
        
        try:
            # 发送用户输入
            response = agent.process_user_input(conv["user_input"], session_id)
            print(f"🤖 实际回复: {response}")
            
            # 分析回复是否符合预期
            expected_keywords = []
            if i == 1:
                expected_keywords = ["去哪", "哪里", "目的地"]
            elif i == 2:
                expected_keywords = ["大兴", "首都", "机场", "哪个"]
            elif i == 3:
                expected_keywords = ["大兴机场", "预计", "分钟", "元", "可以"]
            elif i == 4:
                expected_keywords = ["确认", "叫车", "安排"]
            
            # 检查关键词匹配
            matched_keywords = [kw for kw in expected_keywords if kw in response]
            if matched_keywords:
                print(f"✅ 回复符合预期 (匹配关键词: {matched_keywords})")
            else:
                print(f"⚠️  回复可能不符合预期 (期望关键词: {expected_keywords})")
            
            # 分析参数状态
            print(f"\n📝 状态分析: {conv['analysis']}")
            print_parameter_analysis(agent)
            
            # 显示action历史
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    latest_action = hist[-1]
                    can_execute = latest_action.get("can_execute", False)
                    need_action = latest_action.get("need_user_action", False)
                    action_type = latest_action.get("action_type", "")
                    
                    print(f"\n📋 最新动作状态:")
                    print(f"   可执行: {'✅' if can_execute else '❌'}")
                    if need_action:
                        print(f"   需要用户操作: {action_type}")
                    if latest_action.get("error"):
                        print(f"   错误信息: {latest_action.get('error')}")
            
        except Exception as e:
            print(f"❌ 对话处理出错: {str(e)}")
        
        print("\n" + "."*70)
        if i < len(conversations):
            input("按回车继续下一轮对话...")
    
    print("\n🎉 对话测试完成！")
    print("\n📊 总结:")
    print("1. ✅ 演示了参数从'未填写'到'已确认'的完整流程")
    print("2. ✅ 展示了容错率低的函数需要用户确认的机制") 
    print("3. ✅ 验证了困惑度检查对参数验证的影响")
    print("4. ✅ 确保了只有经过用户确认的参数才能执行打车服务")

def analyze_system_behavior():
    """分析系统行为特点"""
    print("\n" + "="*70)
    print("  系统行为分析")
    print("="*70)
    
    print("\n🔍 参数验证机制特点:")
    print("1. 4状态管理: 未填写 → 未校验 → 已校验待确认 → 已确认")
    print("2. 容错率分类: 低容错率函数需要用户确认，高容错率函数可直接执行")
    print("3. 困惑度检查: 通过POI搜索验证地理位置，避免歧义")
    print("4. 智能补全: 利用环境上下文自动补全缺失信息")
    
    print("\n🛡️ 安全保障措施:")
    print("1. 打车服务(call_taxi_service)被标记为低容错率函数")
    print("2. 所有涉及金钱的操作都需要用户明确确认")
    print("3. 参数验证失败时会阻止函数执行")
    print("4. 完整的状态追踪和错误处理机制")
    
    print("\n💡 用户体验优化:")
    print("1. 自然的对话流程，逐步收集和确认信息")
    print("2. 智能识别模糊输入，主动澄清歧义")
    print("3. 提供详细的路线和价格信息供用户决策")
    print("4. 简洁明了的确认流程")

def main():
    """主函数"""
    print("🚗 打车对话参数验证测试")
    print("测试指定对话序列的参数状态变化")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. 执行指定对话流程测试")
    print("2. 查看系统行为分析")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            test_specific_conversation()
            analyze_system_behavior()
            break
        elif choice == "2":
            analyze_system_behavior()
            break
        elif choice == "3":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    main()
