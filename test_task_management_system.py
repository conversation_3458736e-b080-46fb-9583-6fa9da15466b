#!/usr/bin/env python3
"""
测试任务管理系统
验证：
1. 待确认任务列表的管理
2. 已执行任务列表的管理
3. 参数状态的四种变化：未填写、未校验、已校验待确认、已确认
4. confirm_parameter和cancel_parameter功能
5. 高容错率和低容错率任务的不同处理
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_test_header(title):
    """打印测试标题"""
    print("\n" + "🎯" + "="*78)
    print(f"  {title}")
    print("🎯" + "="*78)

def print_task_status(agent, title="任务状态"):
    """打印当前任务状态"""
    print(f"\n📊 {title}:")
    
    # 打印待确认任务列表
    pending_tasks = agent.enhanced_agent.get_pending_tasks()
    print(f"📋 待确认任务列表 ({len(pending_tasks)}个):")
    for task_id, task_info in pending_tasks.items():
        print(f"   🔄 {task_id}:")
        print(f"      函数: {task_info['function_name']}")
        print(f"      容错率: {task_info['fault_tolerance']}")
        print(f"      状态: {task_info['status']}")
        print(f"      可执行: {'✅' if task_info['executable'] else '❌'}")
        print(f"      参数:")
        for param_name, param_info in task_info['parameters'].items():
            state = param_info['state']
            value = param_info['value']
            confusion_score = param_info.get('confusion_score')
            
            state_icon = {
                "未填写": "⚪",
                "未校验": "🟡", 
                "已校验待确认": "🟠",
                "已确认": "🟢"
            }.get(state, "❓")
            
            print(f"         {state_icon} {param_name}: {value} ({state})")
            if confusion_score is not None:
                print(f"            困惑度: {confusion_score:.2f}")
    
    # 打印已执行任务列表
    executed_tasks = agent.enhanced_agent.get_executed_tasks()
    print(f"\n✅ 已执行任务列表 ({len(executed_tasks)}个):")
    for task_key, task_info in executed_tasks.items():
        print(f"   ✅ {task_key}:")
        print(f"      函数: {task_info['function_name']}")
        print(f"      容错率: {task_info['fault_tolerance']}")
        print(f"      执行时间: {task_info['execution_time']}")
        print(f"      状态: {task_info['status']}")

def test_task_creation_and_parameter_states():
    """测试任务创建和参数状态变化"""
    print_test_header("任务创建和参数状态变化测试")
    
    print("📋 测试目标:")
    print("   1. 测试任务创建")
    print("   2. 验证参数状态的四种变化")
    print("   3. 测试confirm_parameter和cancel_parameter功能")
    
    agent = MainAgent()
    session_id = f"task_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试用例1: 创建不完整的打车任务
    print(f"\n🧪 测试用例1: 创建不完整的打车任务")
    user_input = "我要打车从方正大厦走"
    
    try:
        response = agent.process_user_input(user_input, session_id)
        print(f"👤 用户: {user_input}")
        print(f"🤖 助手: {response}")
        
        print_task_status(agent, "创建任务后的状态")
        
    except Exception as e:
        print(f"❌ 测试用例1出错: {str(e)}")
    
    input("\n按回车继续下一个测试...")
    
    # 测试用例2: 补全参数
    print(f"\n🧪 测试用例2: 补全参数")
    user_input = "去大兴机场"
    
    try:
        response = agent.process_user_input(user_input, session_id)
        print(f"👤 用户: {user_input}")
        print(f"🤖 助手: {response}")
        
        print_task_status(agent, "补全参数后的状态")
        
    except Exception as e:
        print(f"❌ 测试用例2出错: {str(e)}")
    
    input("\n按回车继续下一个测试...")
    
    # 测试用例3: 确认参数
    print(f"\n🧪 测试用例3: 确认参数")
    
    # 获取当前待确认任务
    pending_tasks = agent.enhanced_agent.get_pending_tasks()
    if pending_tasks:
        task_id = list(pending_tasks.keys())[0]
        print(f"📋 找到待确认任务: {task_id}")
        
        # 模拟确认参数
        for param_name in ["start_place", "end_place"]:
            success = agent.enhanced_agent.confirm_parameter(task_id, param_name)
            print(f"✅ 确认参数 {param_name}: {'成功' if success else '失败'}")
        
        print_task_status(agent, "确认参数后的状态")
        
        # 检查是否可执行
        executable_check = agent.enhanced_agent.check_task_executable(task_id)
        print(f"\n🚀 任务可执行性检查: {executable_check}")
        
        if executable_check["can_execute"]:
            print(f"✅ 任务可以执行")
            
            # 执行任务
            execution_result = agent.enhanced_agent.execute_task(task_id)
            print(f"🔄 任务执行结果: {execution_result}")
            
            print_task_status(agent, "执行任务后的状态")
        else:
            print(f"❌ 任务不能执行: {executable_check}")
    else:
        print(f"❌ 没有找到待确认任务")

def test_high_vs_low_fault_tolerance():
    """测试高容错率和低容错率任务的不同处理"""
    print_test_header("高容错率vs低容错率任务处理测试")
    
    print("📋 测试目标:")
    print("   1. 测试高容错率任务的处理（直接移动到已执行列表）")
    print("   2. 测试低容错率任务的处理（需要用户确认）")
    
    agent = MainAgent()
    session_id = f"tolerance_test_{datetime.now().strftime('%H%M%S')}"
    
    # 测试高容错率任务
    print(f"\n🧪 测试高容错率任务: POI搜索")
    user_input = "搜索附近的星巴克"
    
    try:
        response = agent.process_user_input(user_input, session_id)
        print(f"👤 用户: {user_input}")
        print(f"🤖 助手: {response}")
        
        print_task_status(agent, "高容错率任务处理后的状态")
        
    except Exception as e:
        print(f"❌ 高容错率任务测试出错: {str(e)}")
    
    input("\n按回车继续低容错率任务测试...")
    
    # 测试低容错率任务
    print(f"\n🧪 测试低容错率任务: 打车服务")
    user_input = "从方正大厦打车去大兴机场"
    
    try:
        response = agent.process_user_input(user_input, session_id)
        print(f"👤 用户: {user_input}")
        print(f"🤖 助手: {response}")
        
        print_task_status(agent, "低容错率任务处理后的状态")
        
        # 如果有待确认任务，模拟确认流程
        pending_tasks = agent.enhanced_agent.get_pending_tasks()
        for task_id, task_info in pending_tasks.items():
            if task_info['fault_tolerance'] == '低':
                print(f"\n🔄 处理低容错率任务: {task_id}")
                
                # 确认所有参数
                for param_name in task_info['parameters'].keys():
                    agent.enhanced_agent.confirm_parameter(task_id, param_name)
                
                # 执行任务
                execution_result = agent.enhanced_agent.execute_task(task_id)
                print(f"📋 执行结果: {execution_result}")
                
                if execution_result.get("needs_confirmation"):
                    print(f"⏳ 低容错率任务需要用户确认")
                    
                    # 模拟用户确认
                    confirm_success = agent.enhanced_agent.confirm_important_task(task_id)
                    print(f"✅ 用户确认结果: {'成功' if confirm_success else '失败'}")
                
                break
        
        print_task_status(agent, "最终状态")
        
    except Exception as e:
        print(f"❌ 低容错率任务测试出错: {str(e)}")

def test_parameter_operations():
    """测试参数操作功能"""
    print_test_header("参数操作功能测试")
    
    print("📋 测试目标:")
    print("   1. 测试cancel_parameter功能")
    print("   2. 测试参数状态的重置")
    print("   3. 测试参数的重新填写")
    
    agent = MainAgent()
    session_id = f"param_test_{datetime.now().strftime('%H%M%S')}"
    
    # 创建一个任务
    print(f"\n🧪 创建测试任务")
    user_input = "从方正大厦打车去大兴机场"
    
    try:
        response = agent.process_user_input(user_input, session_id)
        print(f"👤 用户: {user_input}")
        print(f"🤖 助手: {response}")
        
        print_task_status(agent, "创建任务后的状态")
        
        # 获取任务ID
        pending_tasks = agent.enhanced_agent.get_pending_tasks()
        if pending_tasks:
            task_id = list(pending_tasks.keys())[0]
            
            # 测试取消参数
            print(f"\n🧪 测试取消参数功能")
            cancel_success = agent.enhanced_agent.cancel_parameter(task_id, "end_place")
            print(f"❌ 取消end_place参数: {'成功' if cancel_success else '失败'}")
            
            print_task_status(agent, "取消参数后的状态")
            
            # 重新设置参数
            print(f"\n🧪 重新设置参数")
            update_success = agent.enhanced_agent.update_task_parameter(
                task_id, "end_place", "首都机场"
            )
            print(f"🔄 重新设置end_place为首都机场: {'成功' if update_success else '失败'}")
            
            print_task_status(agent, "重新设置参数后的状态")
            
        else:
            print(f"❌ 没有找到待确认任务")
        
    except Exception as e:
        print(f"❌ 参数操作测试出错: {str(e)}")

def main():
    """主函数"""
    print("🔧 任务管理系统测试")
    print("测试待确认任务列表、已执行任务列表和参数状态管理")
    
    # 检查环境变量
    required_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n⚠️  警告: 缺少环境变量 {missing_vars}")
        print("部分功能可能无法正常工作")
        
        choice = input("\n是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    print("\n选择测试模式:")
    print("1. 任务创建和参数状态变化测试")
    print("2. 高容错率vs低容错率任务处理测试")
    print("3. 参数操作功能测试")
    print("4. 全部测试")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            test_task_creation_and_parameter_states()
            break
        elif choice == "2":
            test_high_vs_low_fault_tolerance()
            break
        elif choice == "3":
            test_parameter_operations()
            break
        elif choice == "4":
            test_task_creation_and_parameter_states()
            test_high_vs_low_fault_tolerance()
            test_parameter_operations()
            break
        elif choice == "5":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入1-5")

if __name__ == "__main__":
    main()
