#!/usr/bin/env python3
"""
测试对话中function调用的条件补全变化情况
演示4状态参数验证：未填写、未校验、已校验待确认、已确认
确保容错率低的参数都经过用户确认
"""

import os
import sys
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_separator(title=""):
    """打印分隔线"""
    print("\n" + "="*60)
    if title:
        print(f"  {title}")
        print("="*60)

def print_parameter_states(agent, function_name="call_taxi_service"):
    """打印参数状态"""
    if hasattr(agent, 'parameter_manager') and agent.parameter_manager:
        status = agent.parameter_manager.get_function_status(function_name)
        if status.get("status"):
            print(f"\n📊 {function_name} 参数状态:")
            print(f"   容错率: {status.get('fault_tolerance', '未知')}")
            
            parameters = status.get("parameters", {})
            for param_name, param_info in parameters.items():
                state = param_info.get("state", "未知")
                value = param_info.get("value", "无")
                confusion_score = param_info.get("confusion_score")
                
                print(f"   {param_name}: {state}")
                print(f"     值: {value}")
                if confusion_score is not None:
                    print(f"     困惑度: {confusion_score:.2f}")
            
            execution_check = status.get("execution_check", {})
            can_execute = execution_check.get("can_execute", False)
            print(f"   可执行: {'✅' if can_execute else '❌'}")
            
            if not can_execute:
                missing = execution_check.get("missing_required", [])
                unvalidated = execution_check.get("unvalidated_params", [])
                unconfirmed = execution_check.get("unconfirmed_params", [])
                
                if missing:
                    print(f"   缺少必填参数: {missing}")
                if unvalidated:
                    print(f"   未校验参数: {unvalidated}")
                if unconfirmed:
                    print(f"   未确认参数: {unconfirmed}")

def simulate_conversation():
    """模拟对话测试"""
    print_separator("打车对话参数验证测试")
    
    # 初始化Agent
    print("🚀 初始化MainAgent...")
    agent = MainAgent()
    
    # 测试对话序列
    conversations = [
        {
            "user": "打车",
            "description": "用户发起打车请求，系统应询问目的地"
        },
        {
            "user": "去机场",
            "description": "用户说去机场，系统应询问具体哪个机场"
        },
        {
            "user": "大兴",
            "description": "用户确认大兴机场，系统应验证参数并请求确认"
        },
        {
            "user": "好",
            "description": "用户确认参数，系统应执行打车服务"
        }
    ]
    
    session_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    for i, conv in enumerate(conversations, 1):
        print_separator(f"对话轮次 {i}")
        print(f"👤 用户: {conv['user']}")
        print(f"📝 预期: {conv['description']}")
        
        # 处理用户输入
        try:
            response = agent.process_user_input(conv['user'], session_id)
            print(f"🤖 助手: {response}")
            
            # 显示参数状态变化
            print_parameter_states(agent)
            
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
        
        print("\n" + "-"*40)
        input("按回车继续下一轮对话...")

def test_parameter_validation_flow():
    """测试参数验证流程"""
    print_separator("参数验证流程测试")
    
    agent = MainAgent()
    
    # 测试不同的参数状态变化
    test_cases = [
        {
            "name": "缺少参数",
            "function": "call_taxi_service",
            "params": {},
            "expected": "应该提示缺少必填参数"
        },
        {
            "name": "参数不完整",
            "function": "call_taxi_service", 
            "params": {"start_place": "方正大厦"},
            "expected": "应该提示缺少end_place参数"
        },
        {
            "name": "参数完整但未验证",
            "function": "call_taxi_service",
            "params": {"start_place": "方正大厦", "end_place": "大兴机场"},
            "expected": "应该验证参数并等待确认"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        print(f"   函数: {test_case['function']}")
        print(f"   参数: {test_case['params']}")
        print(f"   预期: {test_case['expected']}")
        
        try:
            # 模拟function call
            if hasattr(agent, 'parameter_manager') and agent.parameter_manager:
                # 初始化参数
                init_result = agent.parameter_manager.initialize_function_parameters(
                    test_case['function'], 
                    test_case['params']
                )
                print(f"   初始化结果: {init_result}")
                
                # 验证参数
                if init_result.get("status"):
                    validation_result = agent.parameter_manager.validate_all_parameters(
                        test_case['function']
                    )
                    print(f"   验证结果: {validation_result}")
                    
                    # 检查执行条件
                    execution_check = agent.parameter_manager.can_execute_function(
                        test_case['function']
                    )
                    print(f"   执行检查: {execution_check}")
                    
                    # 显示参数状态
                    print_parameter_states(agent, test_case['function'])
            
        except Exception as e:
            print(f"   ❌ 错误: {str(e)}")
        
        print("\n" + "-"*40)

def test_confirmation_flow():
    """测试确认流程"""
    print_separator("参数确认流程测试")
    
    agent = MainAgent()
    function_name = "call_taxi_service"
    
    # 模拟完整的确认流程
    print("🔄 模拟完整的参数确认流程...")
    
    try:
        if hasattr(agent, 'parameter_manager') and agent.parameter_manager:
            # 1. 初始化参数
            params = {"start_place": "方正大厦", "end_place": "大兴机场"}
            init_result = agent.parameter_manager.initialize_function_parameters(function_name, params)
            print(f"1️⃣ 参数初始化: {init_result.get('status', False)}")
            print_parameter_states(agent, function_name)
            
            # 2. 验证参数
            validation_result = agent.parameter_manager.validate_all_parameters(function_name)
            print(f"\n2️⃣ 参数验证: {validation_result.get('all_valid', False)}")
            print_parameter_states(agent, function_name)
            
            # 3. 确认参数
            for param_name in ["start_place", "end_place"]:
                confirm_result = agent.parameter_manager.confirm_parameter(function_name, param_name)
                print(f"\n3️⃣ 确认 {param_name}: {confirm_result.get('status', False)}")
            
            print_parameter_states(agent, function_name)
            
            # 4. 检查是否可以执行
            execution_check = agent.parameter_manager.can_execute_function(function_name)
            print(f"\n4️⃣ 最终执行检查: {execution_check.get('can_execute', False)}")
            
    except Exception as e:
        print(f"❌ 确认流程错误: {str(e)}")

def main():
    """主函数"""
    print("🎯 打车系统参数验证测试")
    print("测试目标：确保容错率低的参数都经过用户确认")
    
    # 检查环境变量
    required_env_vars = ["AMAP_API_KEY", "BAILIAN_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"⚠️  警告: 缺少环境变量 {missing_vars}")
        print("某些功能可能无法正常工作")
    
    while True:
        print("\n" + "="*60)
        print("请选择测试模式:")
        print("1. 完整对话模拟 (推荐)")
        print("2. 参数验证流程测试")
        print("3. 参数确认流程测试")
        print("4. 退出")
        print("="*60)
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            simulate_conversation()
        elif choice == "2":
            test_parameter_validation_flow()
        elif choice == "3":
            test_confirmation_flow()
        elif choice == "4":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
