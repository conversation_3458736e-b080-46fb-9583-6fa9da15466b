#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然对话演示脚本
使用模拟数据演示优化后的口语化、智能化对话效果
"""

import time
import re
from datetime import datetime


class SmartTaxiAssistant:
    """智能打车助手演示版"""
    
    def __init__(self):
        self.context = {}
        self.current_location = {
            "name": "上地五街方正大厦",
            "address": "北京市海淀区上地五街方正大厦"
        }
        self.current_time = datetime.now()
    
    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息 - 演示版"""
        
        # 初始化会话上下文
        if session_id not in self.context:
            self.context[session_id] = {
                "origin": None,
                "destination": None,
                "car_type": None,
                "time_preference": None
            }
        
        ctx = self.context[session_id]
        
        # 智能意图识别和响应生成
        return self._generate_smart_response(user_input, ctx)
    
    def _generate_smart_response(self, user_input: str, ctx: dict) -> str:
        """生成智能响应"""
        user_input_lower = user_input.lower()
        
        # 场景1：用户说"打车"
        if "打车" in user_input_lower or "叫车" in user_input_lower:
            if not ctx["destination"]:
                return "去哪？"
            else:
                return self._generate_booking_summary(ctx)
        
        # 场景2：用户说目的地
        if self._is_destination_mention(user_input):
            destination = self._extract_destination(user_input)
            
            if "机场" in destination:
                if "大兴" not in destination and "首都" not in destination and "国际" not in destination:
                    return "大兴还是首都机场？"
                else:
                    ctx["destination"] = destination
                    ctx["origin"] = self.current_location["name"]
                    
                    # 检查时间冲突
                    time_conflict = self._check_time_conflict(user_input)
                    if time_conflict:
                        return self._generate_booking_summary_with_time_warning(ctx, time_conflict)
                    else:
                        return self._generate_booking_summary(ctx)
            else:
                ctx["destination"] = destination
                ctx["origin"] = self.current_location["name"]
                return self._generate_booking_summary(ctx)
        
        # 场景3：确认机场选择
        if "大兴" in user_input_lower:
            ctx["destination"] = "大兴机场"
            ctx["origin"] = self.current_location["name"]
            return self._generate_booking_summary(ctx)
        
        if "首都" in user_input_lower or "国际" in user_input_lower:
            ctx["destination"] = "首都机场"
            ctx["origin"] = self.current_location["name"]
            return self._generate_booking_summary(ctx)
        
        # 场景4：价格询问
        if "多少钱" in user_input_lower or "费用" in user_input_lower:
            if ctx["destination"]:
                return self._get_price_info(ctx["destination"])
            else:
                return "去哪呢？"
        
        # 场景5：确认或取消
        if "确认" in user_input_lower or "好的" in user_input_lower or "可以" in user_input_lower:
            return "好的，正在为您叫车，请稍等"
        
        if "取消" in user_input_lower or "不要" in user_input_lower:
            return "好的，已取消"
        
        # 场景6：修改需求
        if "换成" in user_input_lower:
            new_destination = self._extract_destination(user_input)
            if new_destination:
                ctx["destination"] = new_destination
                return self._generate_booking_summary(ctx)
        
        # 场景7：抱怨价格
        if "太贵" in user_input_lower or "便宜" in user_input_lower:
            return "可以选择经济型车辆，或者拼车会更便宜些"
        
        # 默认回复
        return "我可以帮您叫车，请告诉我要去哪里"
    
    def _is_destination_mention(self, user_input: str) -> bool:
        """判断是否提到了目的地"""
        destination_keywords = ["去", "到", "机场", "车站", "商场", "大厦", "医院", "学校"]
        return any(keyword in user_input for keyword in destination_keywords)
    
    def _extract_destination(self, user_input: str) -> str:
        """提取目的地"""
        # 简单的目的地提取逻辑
        if "大兴机场" in user_input or "大兴" in user_input:
            return "大兴机场"
        elif "首都机场" in user_input or "国际机场" in user_input:
            return "首都机场"
        elif "机场" in user_input:
            return "机场"
        elif "西单" in user_input:
            return "西单"
        elif "王府井" in user_input:
            return "王府井"
        else:
            # 提取"去XX"或"到XX"的模式
            patterns = [r"去(\w+)", r"到(\w+)"]
            for pattern in patterns:
                match = re.search(pattern, user_input)
                if match:
                    return match.group(1)
        
        return "目的地"
    
    def _check_time_conflict(self, user_input: str) -> str:
        """检查时间冲突"""
        current_hour = self.current_time.hour
        
        # 检测用户提到的时间
        time_patterns = [
            r"(\d{1,2})点",
            r"晚上(\d{1,2})点",
            r"(\d{1,2}):(\d{2})"
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, user_input)
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) == 2 and match[1]:  # HH:MM格式
                        target_hour = int(match[0])
                    else:  # 晚上X点格式
                        target_hour = int(match[0])
                        if target_hour < 12:
                            target_hour += 12
                else:  # X点格式
                    target_hour = int(match)
                
                # 检查冲突
                time_diff = target_hour - current_hour
                
                if "飞机" in user_input or "航班" in user_input:
                    if time_diff > 3:
                        return f"现在才{current_hour}点，{target_hour}点的飞机，现在打车会太早哦"
                    elif time_diff < 1:
                        return f"现在{current_hour}点，{target_hour}点的飞机，时间有点紧张"
                
                if time_diff > 4:
                    return f"现在{current_hour}点，{target_hour}点出发，要预约吗？"
        
        return ""
    
    def _generate_booking_summary(self, ctx: dict) -> str:
        """生成订单摘要"""
        origin = ctx.get("origin", self.current_location["name"])
        destination = ctx.get("destination", "目的地")
        
        # 根据目的地生成预估信息
        if "大兴机场" in destination:
            return f"从{origin}到大兴机场，预计60分钟，120元，可以吗？"
        elif "首都机场" in destination:
            return f"从{origin}到首都机场，预计45分钟，100元，可以吗？"
        elif "西单" in destination:
            return f"从{origin}到西单，预计25分钟，35元，可以吗？"
        else:
            return f"从{origin}到{destination}，预计30分钟，40元，可以吗？"
    
    def _generate_booking_summary_with_time_warning(self, ctx: dict, time_warning: str) -> str:
        """生成带时间警告的订单摘要"""
        summary = self._generate_booking_summary(ctx)
        return f"{summary.replace('，可以吗？', '')}，不过{time_warning}，您确定要现在打车吗，还是预约一下？"
    
    def _get_price_info(self, destination: str) -> str:
        """获取价格信息"""
        if "大兴机场" in destination:
            return "到大兴机场大概120元"
        elif "首都机场" in destination:
            return "到首都机场大概100元"
        elif "西单" in destination:
            return "到西单大概35元"
        else:
            return "具体费用要看距离，一般起步价13元"


def demo_scenario_1():
    """演示场景1：期望的简洁对话流程"""
    print("🎯 场景1：期望的简洁对话流程")
    print("="*60)
    
    assistant = SmartTaxiAssistant()
    session_id = "demo_1"
    
    conversation = [
        ("打车", "期望：好啊，你要去哪？"),
        ("去机场", "期望：大兴还是首都机场？"),
        ("大兴", "期望：从上地五街方正大厦到大兴机场，预计60分钟，120元，可以吗？")
    ]
    
    for i, (user_input, expected) in enumerate(conversation, 1):
        print(f"\n第{i}轮:")
        print(f"👤 用户: {user_input}")
        print(f"💭 {expected}")
        
        response = assistant.process_message(user_input, session_id)
        print(f"🤖 实际: {response}")
        
        # 评估效果
        if len(response) <= 50:
            print("✅ 回复简洁")
        else:
            print("⚠️  回复较长")
        
        print("-" * 40)
        time.sleep(0.5)


def demo_scenario_2():
    """演示场景2：时间冲突检测"""
    print("\n🎯 场景2：时间冲突检测")
    print("="*60)
    
    assistant = SmartTaxiAssistant()
    session_id = "demo_2"
    
    current_hour = assistant.current_time.hour
    user_input = "帮我打个车从方正大厦去大兴机场，今天晚上8点的飞机"
    expected = f"期望：从上地五街方正大厦到大兴机场，预计60分钟，120元，不过现在才{current_hour}点，8点的飞机，现在打车会太早哦，您确定要现在打车吗，还是预约一下？"
    
    print(f"👤 用户: {user_input}")
    print(f"💭 {expected}")
    
    response = assistant.process_message(user_input, session_id)
    print(f"🤖 实际: {response}")
    
    if "太早" in response or "预约" in response:
        print("✅ 成功检测时间冲突")
    else:
        print("⚠️  未检测到时间冲突")


def demo_natural_conversation():
    """演示自然对话流程"""
    print("\n🎯 场景3：自然对话流程")
    print("="*60)
    
    assistant = SmartTaxiAssistant()
    session_id = "demo_3"
    
    conversations = [
        {
            "name": "简单打车",
            "flow": [
                ("我要去西单", "应该直接提供方案"),
                ("多少钱？", "应该回答价格"),
                ("好的", "应该确认叫车")
            ]
        },
        {
            "name": "机场选择",
            "flow": [
                ("去机场", "应该询问哪个机场"),
                ("首都机场", "应该提供完整方案"),
                ("太贵了", "应该提供替代方案")
            ]
        },
        {
            "name": "修改目的地",
            "flow": [
                ("去大兴机场", "应该提供方案"),
                ("换成首都机场", "应该更新目的地"),
                ("确认", "应该开始叫车")
            ]
        }
    ]
    
    for conv in conversations:
        print(f"\n--- {conv['name']} ---")
        
        for i, (user_input, expectation) in enumerate(conv['flow'], 1):
            print(f"\n第{i}轮:")
            print(f"👤 用户: {user_input}")
            print(f"💭 {expectation}")
            
            response = assistant.process_message(user_input, session_id)
            print(f"🤖 实际: {response}")
            
            # 重置会话以避免上下文干扰
            if i == len(conv['flow']):
                assistant.context[session_id] = {
                    "origin": None,
                    "destination": None,
                    "car_type": None,
                    "time_preference": None
                }
        
        print("-" * 50)


def demo_comparison():
    """对比演示：优化前vs优化后"""
    print("\n🎯 场景4：优化前后对比")
    print("="*60)
    
    test_cases = [
        {
            "input": "打车",
            "old_style": "您好！我是智能打车助手，很高兴为您服务。请问您需要从哪里出发，要去哪个目的地呢？",
            "new_style": "去哪？"
        },
        {
            "input": "去机场",
            "old_style": "好的，我了解您要去机场。请问您是要去北京大兴国际机场还是北京首都国际机场呢？",
            "new_style": "大兴还是首都机场？"
        },
        {
            "input": "大兴机场",
            "old_style": "明白了，您要从当前位置前往北京大兴国际机场。根据预估，距离约60公里，预计行程时间60分钟，费用大约120元人民币。请问您是否确认这个行程安排？",
            "new_style": "从上地五街方正大厦到大兴机场，预计60分钟，120元，可以吗？"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n对比{i}:")
        print(f"👤 用户: {case['input']}")
        print(f"❌ 优化前: {case['old_style']}")
        print(f"✅ 优化后: {case['new_style']}")
        
        old_length = len(case['old_style'])
        new_length = len(case['new_style'])
        reduction = (old_length - new_length) / old_length * 100
        
        print(f"📊 长度对比: {old_length}字 → {new_length}字 (减少{reduction:.1f}%)")
        print("-" * 50)


def main():
    """主演示函数"""
    print("🚀 自然对话效果演示")
    print("="*60)
    print("演示目标：")
    print("1. 简洁、口语化的对话风格")
    print("2. 智能环境信息利用")
    print("3. 时间冲突检测")
    print("4. 自然的对话流程")
    print("="*60)
    
    try:
        demo_scenario_1()
        demo_scenario_2()
        demo_natural_conversation()
        demo_comparison()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
    
    print("\n" + "="*60)
    print("🏁 自然对话演示完成")
    print("="*60)
    print("\n📋 优化效果总结:")
    print("✅ 回复长度大幅减少 (平均减少60-80%)")
    print("✅ 语言更加口语化和自然")
    print("✅ 智能利用环境信息补全")
    print("✅ 主动检测和提醒冲突")
    print("✅ 对话流程更加顺畅")
    print("\n💡 核心改进:")
    print("- 去掉冗余的客套话")
    print("- 使用简短直接的问句")
    print("- 主动利用位置和时间信息")
    print("- 智能检测用户需求冲突")


if __name__ == "__main__":
    main()
