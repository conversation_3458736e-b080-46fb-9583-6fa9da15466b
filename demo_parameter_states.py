#!/usr/bin/env python3
"""
演示打车对话中function调用的4状态参数验证
展示：未填写 -> 未校验 -> 已校验待确认 -> 已确认
确保容错率低的参数都经过用户确认
"""

import os
import json
from datetime import datetime
from taxi_agent_system import MainAgent

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_parameter_status(agent, function_name="call_taxi_service"):
    """打印参数状态详情"""
    if not hasattr(agent.enhanced_agent, 'parameter_manager') or not agent.enhanced_agent.parameter_manager:
        print("❌ 参数管理器未初始化")
        return
    
    try:
        status = agent.enhanced_agent.parameter_manager.get_function_status(function_name)
        if not status.get("status"):
            print(f"❌ 获取函数状态失败: {status.get('error', '未知错误')}")
            return
        
        print(f"\n📊 {function_name} 参数状态详情:")
        print(f"   🔧 容错率: {status.get('fault_tolerance', '未知')}")
        
        parameters = status.get("parameters", {})
        if not parameters:
            print("   📝 暂无参数")
            return
            
        for param_name, param_info in parameters.items():
            state = param_info.get("state", "未知")
            value = param_info.get("value", "无")
            confusion_score = param_info.get("confusion_score")
            
            # 状态图标
            state_icon = {
                "未填写": "⚪",
                "未校验": "🟡", 
                "已校验待确认": "🟠",
                "已确认": "🟢"
            }.get(state, "❓")
            
            print(f"   {state_icon} {param_name}: {state}")
            print(f"      值: {value}")
            if confusion_score is not None:
                confusion_level = "低" if confusion_score <= 0.2 else "中" if confusion_score <= 0.5 else "高"
                print(f"      困惑度: {confusion_score:.2f} ({confusion_level})")
        
        execution_check = status.get("execution_check", {})
        can_execute = execution_check.get("can_execute", False)
        print(f"\n   🚀 可执行状态: {'✅ 可以执行' if can_execute else '❌ 不可执行'}")
        
        if not can_execute:
            missing = execution_check.get("missing_required", [])
            unvalidated = execution_check.get("unvalidated_params", [])
            unconfirmed = execution_check.get("unconfirmed_params", [])
            
            if missing:
                print(f"      ⚠️  缺少必填参数: {missing}")
            if unvalidated:
                print(f"      ⚠️  未校验参数: {unvalidated}")
            if unconfirmed:
                print(f"      ⚠️  未确认参数: {unconfirmed}")
                
    except Exception as e:
        print(f"❌ 获取参数状态出错: {str(e)}")

def simulate_taxi_conversation():
    """模拟完整的打车对话，展示参数状态变化"""
    print_header("打车对话参数状态变化演示")
    
    print("🎯 目标：演示4状态参数验证流程")
    print("   1. 未填写 -> 2. 未校验 -> 3. 已校验待确认 -> 4. 已确认")
    print("   确保容错率低的函数参数都经过用户确认")
    
    # 初始化Agent
    print("\n🚀 初始化MainAgent...")
    agent = MainAgent()
    session_id = f"demo_{datetime.now().strftime('%H%M%S')}"
    
    # 对话序列
    conversations = [
        {
            "user": "打车",
            "description": "用户发起打车请求",
            "expected_state": "系统询问目的地，参数处于'未填写'状态"
        },
        {
            "user": "去机场", 
            "description": "用户说去机场（模糊地点）",
            "expected_state": "系统询问具体机场，参数仍为'未填写'或'未校验'"
        },
        {
            "user": "大兴机场",
            "description": "用户明确大兴机场",
            "expected_state": "参数变为'未校验'，系统进行验证后变为'已校验待确认'"
        },
        {
            "user": "确认",
            "description": "用户确认参数",
            "expected_state": "参数变为'已确认'，系统执行打车服务"
        }
    ]
    
    for i, conv in enumerate(conversations, 1):
        print_header(f"对话轮次 {i}")
        print(f"👤 用户输入: {conv['user']}")
        print(f"📝 描述: {conv['description']}")
        print(f"🎯 预期状态: {conv['expected_state']}")
        
        try:
            # 处理用户输入
            response = agent.process_user_input(conv['user'], session_id)
            print(f"\n🤖 系统回复: {response}")
            
            # 显示参数状态
            print_parameter_status(agent)
            
            # 显示action_state_hist
            if hasattr(agent.enhanced_agent, 'action_state_hist'):
                hist = agent.enhanced_agent.action_state_hist
                if hist:
                    print(f"\n📋 动作状态历史 (最近{len(hist)}条):")
                    for j, action in enumerate(hist[-3:], 1):  # 显示最近3条
                        can_execute = action.get("can_execute", False)
                        need_action = action.get("need_user_action", False)
                        action_type = action.get("action_type", "未知")
                        
                        print(f"   {j}. 可执行: {'✅' if can_execute else '❌'}")
                        if need_action:
                            print(f"      需要用户操作: {action_type}")
                        if action.get("error"):
                            print(f"      错误: {action.get('error')}")
            
        except Exception as e:
            print(f"❌ 处理出错: {str(e)}")
        
        print("\n" + "-"*40)
        if i < len(conversations):
            input("按回车继续下一轮对话...")

def demonstrate_parameter_validation():
    """演示参数验证的详细过程"""
    print_header("参数验证详细过程演示")
    
    agent = MainAgent()
    
    # 模拟不同的参数状态
    test_scenarios = [
        {
            "name": "场景1: 参数完全缺失",
            "function": "call_taxi_service",
            "params": {},
            "description": "所有参数都处于'未填写'状态"
        },
        {
            "name": "场景2: 部分参数提供",
            "function": "call_taxi_service", 
            "params": {"start_place": "方正大厦"},
            "description": "start_place为'未校验'，end_place为'未填写'"
        },
        {
            "name": "场景3: 参数完整但未验证",
            "function": "call_taxi_service",
            "params": {"start_place": "方正大厦", "end_place": "大兴机场"},
            "description": "两个参数都为'未校验'状态"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🧪 {scenario['name']}")
        print(f"   📝 {scenario['description']}")
        print(f"   🔧 函数: {scenario['function']}")
        print(f"   📊 参数: {scenario['params']}")
        
        try:
            if hasattr(agent.enhanced_agent, 'parameter_manager') and agent.enhanced_agent.parameter_manager:
                # 初始化参数
                init_result = agent.enhanced_agent.parameter_manager.initialize_function_parameters(
                    scenario['function'], 
                    scenario['params']
                )
                
                print(f"\n   ✅ 初始化结果: {init_result.get('status', False)}")
                if init_result.get('missing_required'):
                    print(f"   ⚠️  缺少必填参数: {init_result['missing_required']}")
                
                # 显示初始化后的参数状态
                print_parameter_status(agent, scenario['function'])
                
                # 如果有参数，尝试验证
                if scenario['params']:
                    print(f"\n   🔍 开始验证参数...")
                    validation_result = agent.enhanced_agent.parameter_manager.validate_all_parameters(
                        scenario['function']
                    )
                    print(f"   ✅ 验证结果: {validation_result.get('all_valid', False)}")
                    
                    # 显示验证后的参数状态
                    print_parameter_status(agent, scenario['function'])
                    
                    # 检查执行条件
                    execution_check = agent.enhanced_agent.parameter_manager.can_execute_function(
                        scenario['function']
                    )
                    print(f"\n   🚀 执行检查: {execution_check.get('can_execute', False)}")
                    
                    if not execution_check.get('can_execute'):
                        fault_tolerance = execution_check.get('fault_tolerance', '未知')
                        print(f"   🔧 容错率: {fault_tolerance}")
                        if fault_tolerance == "低":
                            print("   ⚠️  低容错率函数需要用户确认所有参数")
                
        except Exception as e:
            print(f"   ❌ 测试出错: {str(e)}")
        
        print("\n" + "."*50)

def main():
    """主函数"""
    print("🎯 打车系统参数验证演示")
    print("展示4状态参数验证：未填写 -> 未校验 -> 已校验待确认 -> 已确认")
    
    # 检查环境变量
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  警告: 未设置BAILIAN_API_KEY，对话功能可能受限")
    
    print("\n选择演示模式:")
    print("1. 完整对话演示 (推荐)")
    print("2. 参数验证详细过程")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            simulate_taxi_conversation()
            break
        elif choice == "2":
            demonstrate_parameter_validation()
            break
        elif choice == "3":
            print("👋 演示结束")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    main()
